<?php
/**
 * AI Provider Abstract Class
 *
 * This class serves as a base for all AI provider implementations.
 */
abstract class AI<PERSON>rovider {
    protected $db;
    protected $providerId;
    protected $apiKey;
    protected $apiKeyId;

    /**
     * Constructor
     *
     * @param Database $db Database instance
     * @param int $providerId Provider ID
     * @param string $apiKey API key
     * @param int $apiKeyId API key ID
     */
    public function __construct(Database $db, $providerId, $apiKey, $apiKeyId) {
        $this->db = $db;
        $this->providerId = $providerId;
        $this->apiKey = $apiKey;
        $this->apiKeyId = $apiKeyId;
    }

    /**
     * Generate text using the provider's API
     *
     * @param string $prompt The prompt to send to the API
     * @param array $options Additional options for the API call
     * @return array Response data including generated text and metadata
     */
    abstract public function generateText($prompt, $options = []);

    /**
     * Generate image using the provider's API
     *
     * @param string $prompt The prompt to send to the API
     * @param array $options Additional options for the API call
     * @return array Response data including image URL and metadata
     */
    abstract public function generateImage($prompt, $options = []);

    /**
     * Log API usage
     *
     * @param int $processedPostId Processed post ID
     * @param string $stepType Step type (title, content, etc.)
     * @param string $taskType Task type (translate, rewrite, etc.)
     * @param int $modelId Model ID
     * @param string $prompt Prompt sent to the API
     * @param string $response Response from the API
     * @param int $tokensUsed Number of tokens used
     * @param float $executionTime Execution time in seconds
     * @param string $status Status (success or failed)
     * @param string $errorMessage Error message if any
     * @return int Log ID
     */
    protected function logApiUsage($processedPostId, $stepType, $taskType, $modelId, $prompt, $response, $tokensUsed, $executionTime, $status = 'success', $errorMessage = null) {
        // Update API key usage count
        $this->db->query("UPDATE ai_api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = ?", [$this->apiKeyId]);

        // Log API usage
        return $this->db->insert('ai_processing_logs', [
            'processed_post_id' => $processedPostId,
            'step_type' => $stepType,
            'task_type' => $taskType,
            'model_id' => $modelId,
            'api_key_id' => $this->apiKeyId,
            'prompt' => $prompt,
            'response' => $response,
            'tokens_used' => $tokensUsed,
            'execution_time' => $executionTime,
            'status' => $status,
            'error_message' => $errorMessage
        ]);
    }

    /**
     * Factory method to create an AI provider instance
     *
     * @param Database $db Database instance
     * @param string $providerSlug Provider slug
     * @param string $apiKey Optional API key (if not provided, will use default)
     * @return AIProvider AI provider instance
     */
    public static function create(Database $db, $providerSlug, $apiKey = null) {
        // Get provider data
        $provider = $db->getRow("SELECT * FROM ai_providers WHERE slug = ? AND is_active = 1", [$providerSlug]);
        if (!$provider) {
            throw new Exception("AI provider '$providerSlug' not found or not active");
        }

        // Get API key
        if ($apiKey) {
            $apiKeyData = $db->getRow("SELECT * FROM ai_api_keys WHERE provider_id = ? AND api_key = ? AND is_active = 1", [$provider['id'], $apiKey]);
            if (!$apiKeyData) {
                throw new Exception("API key not found or not active for provider '$providerSlug'");
            }
        } else {
            $apiKeyData = $db->getRow("SELECT * FROM ai_api_keys WHERE provider_id = ? AND is_default = 1 AND is_active = 1", [$provider['id']]);
            if (!$apiKeyData) {
                // Try to get any active API key
                $apiKeyData = $db->getRow("SELECT * FROM ai_api_keys WHERE provider_id = ? AND is_active = 1 ORDER BY last_used ASC LIMIT 1", [$provider['id']]);
                if (!$apiKeyData) {
                    throw new Exception("No active API key found for provider '$providerSlug'");
                }
            }
        }

        // Create provider instance based on slug
        // Map provider slugs to correct class names
        $classNameMap = [
            'openrouter' => 'OpenrouterAIProvider',
            'novita' => 'NovitaAIProvider',
            'openai' => 'OpenaiAIProvider',
            'anthropic' => 'AnthropicAIProvider'
        ];

        $providerClass = $classNameMap[$providerSlug] ?? ucfirst($providerSlug) . 'AIProvider';
        $providerFile = __DIR__ . '/AIProviders/' . $providerClass . '.php';

        if (!file_exists($providerFile)) {
            throw new Exception("AI provider class file not found: $providerFile");
        }

        require_once $providerFile;

        if (!class_exists($providerClass)) {
            throw new Exception("AI provider class not found: $providerClass");
        }

        return new $providerClass($db, $provider['id'], $apiKeyData['api_key'], $apiKeyData['id']);
    }
}
?>
