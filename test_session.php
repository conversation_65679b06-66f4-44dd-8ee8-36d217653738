<?php
/**
 * Test Session
 * 
 * This script tests session handling for the API.
 */

// Load configuration
require_once 'config.php';

// Start session
session_start();

header('Content-Type: text/plain');

echo "=== SESSION TEST ===\n\n";

echo "Session ID: " . session_id() . "\n";
echo "Session status: " . session_status() . "\n";
echo "Session name: " . session_name() . "\n";
echo "Session save path: " . session_save_path() . "\n\n";

echo "Session data:\n";
foreach ($_SESSION as $key => $value) {
    echo "  $key: $value\n";
}

if (!isset($_SESSION['user_id'])) {
    echo "\n❌ No user_id in session!\n";
    echo "Setting user_id to 1 for testing...\n";
    $_SESSION['user_id'] = 1;
    echo "✅ user_id set to 1\n";
} else {
    echo "\n✅ user_id found: {$_SESSION['user_id']}\n";
}

echo "\nCookies:\n";
foreach ($_COOKIE as $key => $value) {
    echo "  $key: $value\n";
}

echo "\nTesting API endpoint with current session...\n";

// Test API call with current session
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, BASE_URL . '/api/ai_lab.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['action' => 'test_connection']));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "API Response HTTP Code: $httpCode\n";
echo "API Response: $response\n";

$decoded = json_decode($response, true);
if ($decoded) {
    if ($decoded['success']) {
        echo "✅ API test successful with session!\n";
    } else {
        echo "❌ API test failed: {$decoded['error']}\n";
    }
} else {
    echo "❌ API response is not valid JSON\n";
    if (strpos($response, '<!DOCTYPE') !== false || strpos($response, '<html') !== false) {
        echo "⚠️  Response appears to be HTML (redirect detected)\n";
    }
}

echo "\n=== SESSION TEST COMPLETE ===\n";
?>
