<?php
/**
 * Setup Wizard
 *
 * This file handles the setup process for the application.
 */

// Define base path
define('BASE_PATH', dirname(dirname(__FILE__)));

// Define setup steps
$steps = [
    1 => 'Welcome',
    2 => 'Database Setup',
    3 => 'User Setup',
    4 => 'Finish'
];

// Get current step
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
if ($step < 1 || $step > count($steps)) {
    $step = 1;
}

// Handle form submissions
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // Database setup
            $dbHost = $_POST['db_host'] ?? '';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';

            // Validate inputs
            if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
                $error = 'All fields are required except password (if your database has no password).';
            } else {
                // Test database connection
                try {
                    $conn = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
                    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    // Create database if it doesn't exist
                    $conn->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                    // Select the database
                    $conn->exec("USE `$dbName`");

                    // Create tables
                    $tables = [
                        "CREATE TABLE IF NOT EXISTS `jobs` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `url` varchar(255) NOT NULL,
                            `category_id` int(11) DEFAULT NULL,
                            `type` enum('wordpress','sitemap') NOT NULL DEFAULT 'wordpress',
                            `category` varchar(255) DEFAULT NULL,
                            `disable_embed` tinyint(1) NOT NULL DEFAULT 0,
                            `posts_per_run` int(11) NOT NULL DEFAULT 10,
                            `after_date` date DEFAULT NULL,
                            `before_date` date DEFAULT NULL,
                            `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
                            `schedule_type` varchar(50) DEFAULT NULL,
                            `frequency` varchar(50) DEFAULT 'daily',
                            `last_run` datetime DEFAULT NULL,
                            `last_run_posts` int(11) DEFAULT 0,
                            `error` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `category_id` (`category_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `posts` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `job_id` int(11) NOT NULL,
                            `external_id` varchar(255) NOT NULL,
                            `title` varchar(255) NOT NULL,
                            `content` longtext NOT NULL,
                            `excerpt` text DEFAULT NULL,
                            `url` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `date_published` datetime NOT NULL,
                            `date_modified` datetime NOT NULL,
                            `featured_image` text DEFAULT NULL,
                            `html_file` varchar(255) DEFAULT NULL,
                            `pdf_file` varchar(255) DEFAULT NULL,
                            `metadata` text DEFAULT NULL,
                            `processed` tinyint(1) NOT NULL DEFAULT 0,
                            `is_future` tinyint(1) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `external_id` (`external_id`),
                            UNIQUE KEY `url` (`url`),
                            KEY `job_id` (`job_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `categories` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `created_at` datetime NOT NULL,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `name` (`name`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `tags` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `created_at` datetime NOT NULL,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `name` (`name`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `post_categories` (
                            `post_id` int(11) NOT NULL,
                            `category_id` int(11) NOT NULL,
                            PRIMARY KEY (`post_id`,`category_id`),
                            KEY `category_id` (`category_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `post_tags` (
                            `post_id` int(11) NOT NULL,
                            `tag_id` int(11) NOT NULL,
                            PRIMARY KEY (`post_id`,`tag_id`),
                            KEY `tag_id` (`tag_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `images` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `post_id` int(11) NOT NULL,
                            `url` varchar(255) NOT NULL,
                            `local_path` varchar(255) NOT NULL,
                            `optimized_path` varchar(255) DEFAULT NULL,
                            `alt` varchar(255) DEFAULT NULL,
                            `caption` text DEFAULT NULL,
                            `is_featured` tinyint(1) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `post_id` (`post_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `users` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `username` varchar(255) NOT NULL,
                            `password` varchar(255) NOT NULL,
                            `email` varchar(255) NOT NULL,
                            `role` enum('admin','editor','viewer') NOT NULL DEFAULT 'viewer',
                            `api_key` varchar(255) DEFAULT NULL,
                            `remember_token` varchar(255) DEFAULT NULL,
                            `token_expires` datetime DEFAULT NULL,
                            `last_login` datetime DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `username` (`username`),
                            UNIQUE KEY `email` (`email`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `job_runs` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `job_id` int(11) NOT NULL,
                            `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
                            `start_time` datetime NOT NULL,
                            `end_time` datetime DEFAULT NULL,
                            `posts_grabbed` int(11) NOT NULL DEFAULT 0,
                            `error` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `job_id` (`job_id`),
                            KEY `status` (`status`),
                            KEY `start_time` (`start_time`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `job_stats` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `job_id` int(11) NOT NULL,
                            `run_date` datetime NOT NULL,
                            `total_posts` int(11) NOT NULL DEFAULT 0,
                            `new_posts` int(11) NOT NULL DEFAULT 0,
                            `updated_posts` int(11) NOT NULL DEFAULT 0,
                            `skipped_posts` int(11) NOT NULL DEFAULT 0,
                            `execution_time` float NOT NULL DEFAULT 0,
                            `approaches_tried` int(11) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `job_id` (`job_id`),
                            KEY `run_date` (`run_date`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `settings` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `key` varchar(255) NOT NULL,
                            `value` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `key` (`key`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_providers` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `description` text DEFAULT NULL,
                            `website` varchar(255) DEFAULT NULL,
                            `api_base_url` varchar(500) DEFAULT NULL,
                            `auth_type` enum('bearer','api_key','custom') NOT NULL DEFAULT 'bearer',
                            `custom_headers` json DEFAULT NULL,
                            `rate_limit_per_minute` int(11) DEFAULT NULL,
                            `supports_streaming` tinyint(1) NOT NULL DEFAULT 0,
                            `is_active` tinyint(1) NOT NULL DEFAULT 1,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `slug` (`slug`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_api_keys` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `provider_id` int(11) NOT NULL,
                            `name` varchar(255) NOT NULL,
                            `api_key` varchar(255) NOT NULL,
                            `is_default` tinyint(1) NOT NULL DEFAULT 0,
                            `is_active` tinyint(1) NOT NULL DEFAULT 1,
                            `usage_count` int(11) NOT NULL DEFAULT 0,
                            `last_used` datetime DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `provider_id` (`provider_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_models` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `provider_id` int(11) NOT NULL,
                            `name` varchar(255) NOT NULL,
                            `model_id` varchar(255) NOT NULL,
                            `type` enum('text','image','both') NOT NULL DEFAULT 'text',
                            `capabilities` json DEFAULT NULL,
                            `system_prompt` text DEFAULT NULL,
                            `max_tokens` int(11) DEFAULT NULL,
                            `temperature` float DEFAULT 0.7,
                            `is_active` tinyint(1) NOT NULL DEFAULT 1,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `provider_id` (`provider_id`),
                            KEY `type` (`type`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_workflows` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `description` text DEFAULT NULL,
                            `is_default` tinyint(1) NOT NULL DEFAULT 0,
                            `is_active` tinyint(1) NOT NULL DEFAULT 1,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_workflow_steps` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `workflow_id` int(11) NOT NULL,
                            `model_id` int(11) NOT NULL,
                            `step_type` enum('title','content','image_prompt','image_generation','category','tag','seo') NOT NULL,
                            `task_type` enum('translate','rewrite','translate_rewrite','generate','optimize') NOT NULL,
                            `prompt_template` text DEFAULT NULL,
                            `order_index` int(11) NOT NULL DEFAULT 0,
                            `is_active` tinyint(1) NOT NULL DEFAULT 1,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `workflow_id` (`workflow_id`),
                            KEY `model_id` (`model_id`),
                            KEY `step_type` (`step_type`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_processed_posts` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `post_id` int(11) NOT NULL,
                            `workflow_id` int(11) NOT NULL,
                            `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
                            `language` varchar(10) DEFAULT 'en',
                            `is_rtl` tinyint(1) NOT NULL DEFAULT 0,
                            `processed_title` text DEFAULT NULL,
                            `processed_content` longtext DEFAULT NULL,
                            `processed_excerpt` text DEFAULT NULL,
                            `image_prompt` text DEFAULT NULL,
                            `processed_featured_image` varchar(255) DEFAULT NULL,
                            `processed_categories` text DEFAULT NULL,
                            `processed_tags` text DEFAULT NULL,
                            `processed_seo_metadata` text DEFAULT NULL,
                            `error_message` text DEFAULT NULL,
                            `started_at` datetime DEFAULT NULL,
                            `completed_at` datetime DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `post_id` (`post_id`),
                            KEY `workflow_id` (`workflow_id`),
                            KEY `status` (`status`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_processing_logs` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `processed_post_id` int(11) NOT NULL,
                            `step_type` enum('title','content','image_prompt','image_generation','category','tag','seo') NOT NULL,
                            `task_type` enum('translate','rewrite','translate_rewrite','generate','optimize') NOT NULL,
                            `model_id` int(11) NOT NULL,
                            `api_key_id` int(11) NOT NULL,
                            `prompt` text DEFAULT NULL,
                            `response` text DEFAULT NULL,
                            `tokens_used` int(11) DEFAULT NULL,
                            `execution_time` float DEFAULT NULL,
                            `status` enum('success','failed') NOT NULL DEFAULT 'success',
                            `error_message` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `processed_post_id` (`processed_post_id`),
                            KEY `model_id` (`model_id`),
                            KEY `api_key_id` (`api_key_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_lab_conversations` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `user_id` int(11) NOT NULL,
                            `title` varchar(255) DEFAULT NULL,
                            `provider_id` int(11) NOT NULL,
                            `model_id` int(11) NOT NULL,
                            `is_active` tinyint(1) NOT NULL DEFAULT 1,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `user_id` (`user_id`),
                            KEY `provider_id` (`provider_id`),
                            KEY `model_id` (`model_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `ai_lab_messages` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `conversation_id` int(11) NOT NULL,
                            `role` enum('user','assistant','system') NOT NULL,
                            `content` longtext NOT NULL,
                            `tokens_used` int(11) DEFAULT NULL,
                            `execution_time` float DEFAULT NULL,
                            `model_used` varchar(255) DEFAULT NULL,
                            `error_message` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `conversation_id` (`conversation_id`)
                        )"
                    ];

                    foreach ($tables as $table) {
                        $conn->exec($table);
                    }

                    // Populate AI tables with default data
                    // Insert default AI provider (NOVITA)
                    $stmt = $conn->prepare("INSERT IGNORE INTO ai_providers (name, slug, description, website, is_active) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute(['NOVITA AI', 'novita', 'NOVITA AI provides advanced text and image generation models with state-of-the-art capabilities.', 'https://novita.ai', 1]);

                    // Get provider ID
                    $stmt = $conn->prepare("SELECT id FROM ai_providers WHERE slug = 'novita'");
                    $stmt->execute();
                    $providerId = $stmt->fetchColumn();

                    if ($providerId) {
                        // Insert default API key
                        $stmt = $conn->prepare("INSERT IGNORE INTO ai_api_keys (provider_id, name, api_key, is_default, is_active, usage_count) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$providerId, 'NOVITA AI Default Key', 'sk-novita-default-key-' . time(), 1, 1, 0]);

                        // Insert default models
                        $defaultModels = [
                            ['NOVITA GPT-4', 'gpt-4', 'text', '["translate", "rewrite", "summarize", "optimize"]', 'You are a helpful assistant that can translate, rewrite, and optimize content.', 4096, 0.7],
                            ['NOVITA GPT-3.5 Turbo', 'gpt-3.5-turbo', 'text', '["translate", "rewrite", "summarize"]', 'You are a helpful assistant that can translate, rewrite, and optimize content.', 4096, 0.7],
                            ['NOVITA Claude 3 Opus', 'claude-3-opus', 'text', '["translate", "rewrite", "summarize", "optimize"]', 'You are a helpful assistant that can translate, rewrite, and optimize content.', 4096, 0.7],
                            ['NOVITA DALL-E 3', 'dall-e-3', 'image', '["generate"]', null, null, null],
                            ['NOVITA Stable Diffusion XL', 'stable-diffusion-xl', 'image', '["generate"]', null, null, null],
                            ['NOVITA Midjourney', 'midjourney', 'image', '["generate"]', null, null, null]
                        ];

                        $stmt = $conn->prepare("INSERT IGNORE INTO ai_models (provider_id, name, model_id, type, capabilities, system_prompt, max_tokens, temperature, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                        foreach ($defaultModels as $model) {
                            $stmt->execute([$providerId, $model[0], $model[1], $model[2], $model[3], $model[4], $model[5], $model[6], 1]);
                        }

                        // Insert default workflow
                        $stmt = $conn->prepare("INSERT IGNORE INTO ai_workflows (name, description, is_default, is_active) VALUES (?, ?, ?, ?)");
                        $stmt->execute(['Default Workflow', 'Default workflow for AI processing', 1, 1]);

                        // Get workflow ID
                        $stmt = $conn->prepare("SELECT id FROM ai_workflows WHERE is_default = 1");
                        $stmt->execute();
                        $workflowId = $stmt->fetchColumn();

                        // Get model IDs
                        $stmt = $conn->prepare("SELECT id FROM ai_models WHERE type = 'text' AND provider_id = ? LIMIT 1");
                        $stmt->execute([$providerId]);
                        $textModelId = $stmt->fetchColumn();

                        $stmt = $conn->prepare("SELECT id FROM ai_models WHERE type = 'image' AND provider_id = ? LIMIT 1");
                        $stmt->execute([$providerId]);
                        $imageModelId = $stmt->fetchColumn();

                        if ($workflowId && $textModelId && $imageModelId) {
                            // Insert default workflow steps
                            $defaultSteps = [
                                [$workflowId, $textModelId, 'title', 'translate', 'Translate the following title to {{language}} while preserving its meaning and SEO value: {{title}}', 1],
                                [$workflowId, $textModelId, 'title', 'rewrite', 'Rewrite the following title to make it more engaging and SEO-friendly while preserving its core meaning: {{title}}', 2],
                                [$workflowId, $textModelId, 'title', 'translate_rewrite', 'Translate and rewrite the following title to {{language}}, making it more engaging and SEO-friendly while preserving its core meaning: {{title}}', 3],
                                [$workflowId, $textModelId, 'content', 'translate', 'Translate the following content to {{language}} while preserving its formatting, meaning, and SEO value: {{content}}', 4],
                                [$workflowId, $textModelId, 'content', 'rewrite', 'Rewrite the following content to make it more engaging and SEO-friendly while preserving its core meaning and HTML formatting: {{content}}', 5],
                                [$workflowId, $textModelId, 'content', 'translate_rewrite', 'Translate and rewrite the following content to {{language}}, making it more engaging and SEO-friendly while preserving its core meaning and HTML formatting: {{content}}', 6],
                                [$workflowId, $textModelId, 'image_prompt', 'generate', 'Create a detailed image generation prompt for a feature image that represents the following article. The prompt should be descriptive and creative: {{title}} {{excerpt}}', 7],
                                [$workflowId, $imageModelId, 'image_generation', 'generate', '{{image_prompt}}', 8],
                                [$workflowId, $textModelId, 'category', 'generate', 'Based on the following article, suggest 3-5 relevant categories. Return only the category names as a comma-separated list: {{title}} {{excerpt}}', 9],
                                [$workflowId, $textModelId, 'tag', 'generate', 'Based on the following article, suggest 5-10 relevant tags. Return only the tag names as a comma-separated list: {{title}} {{excerpt}}', 10],
                                [$workflowId, $textModelId, 'seo', 'optimize', 'Generate SEO metadata for the following content. Include meta description (max 160 characters), keywords (5-7 comma-separated), and a slug: {{title}} {{excerpt}}', 11]
                            ];

                            $stmt = $conn->prepare("INSERT IGNORE INTO ai_workflow_steps (workflow_id, model_id, step_type, task_type, prompt_template, order_index, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)");
                            foreach ($defaultSteps as $step) {
                                $stmt->execute([$step[0], $step[1], $step[2], $step[3], $step[4], $step[5], 1]);
                            }
                        }
                    }

                    // Create config file
                    $configContent = file_get_contents(BASE_PATH . '/config.php');
                    $configContent = str_replace("define('DB_HOST', '');", "define('DB_HOST', '$dbHost');", $configContent);
                    $configContent = str_replace("define('DB_NAME', '');", "define('DB_NAME', '$dbName');", $configContent);
                    $configContent = str_replace("define('DB_USER', '');", "define('DB_USER', '$dbUser');", $configContent);
                    $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$dbPass');", $configContent);

                    file_put_contents(BASE_PATH . '/config.php', $configContent);

                    $success = 'Database setup completed successfully!';

                    // Redirect to next step
                    header('Location: index.php?step=3');
                    exit;
                } catch (PDOException $e) {
                    $error = 'Database connection failed: ' . $e->getMessage();
                }
            }
            break;

        case 3:
            // User setup
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            $email = $_POST['email'] ?? '';

            // Validate inputs
            if (empty($username) || empty($password) || empty($email)) {
                $error = 'All fields are required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Invalid email address.';
            } else {
                // Load config
                require_once BASE_PATH . '/config.php';

                try {
                    // Connect to database
                    $conn = get_db_connection();

                    // Hash password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    // Insert user
                    $stmt = $conn->prepare("INSERT INTO users (username, password, email, created_at) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$username, $hashedPassword, $email, date('Y-m-d H:i:s')]);

                    $success = 'User created successfully!';

                    // Redirect to next step
                    header('Location: index.php?step=4');
                    exit;
                } catch (PDOException $e) {
                    $error = 'Error creating user: ' . $e->getMessage();
                }
            }
            break;
    }
}

// Page title
$pageTitle = 'Setup - ' . $steps[$step];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --primary-light: #dbeafe;
            --success-color: #10b981;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --border-radius: 0.75rem;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-top: 2rem;
            min-height: 100vh;
        }

        .setup-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-xl);
            padding: 2.5rem;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
        }

        .setup-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .setup-header h1 {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .setup-header p {
            color: var(--text-secondary);
            font-size: 1.125rem;
        }

        .setup-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2.5rem;
            position: relative;
            padding: 0 2rem;
        }

        .setup-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 2rem;
            right: 2rem;
            height: 3px;
            background: linear-gradient(90deg, var(--border-color) 0%, var(--primary-light) 100%);
            border-radius: 2px;
            z-index: 1;
        }

        .step {
            position: relative;
            z-index: 2;
            background: white;
            text-align: center;
            padding: 0 1rem;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            font-weight: 600;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            border: 3px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .step.active .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .setup-content {
            margin-bottom: 2rem;
            min-height: 300px;
        }

        .setup-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
            color: white;
        }

        .btn-outline-secondary {
            background: transparent;
            border: 2px solid var(--border-color);
            color: var(--text-secondary);
        }

        .btn-outline-secondary:hover {
            background: var(--text-secondary);
            color: white;
            transform: translateY(-1px);
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .alert {
            border: none;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            border-left: 4px solid;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            color: #dc2626;
            border-left-color: #ef4444;
        }

        .alert-success {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: #059669;
            border-left-color: #10b981;
        }

        @media (max-width: 768px) {
            .setup-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .setup-steps {
                padding: 0 1rem;
            }

            .setup-header h1 {
                font-size: 2rem;
            }

            .step-number {
                width: 35px;
                height: 35px;
            }

            .step-label {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <div class="mb-3">
                    <i class="fas fa-rocket fa-3x" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"></i>
                </div>
                <h1><i class="fas fa-download me-2"></i>Content Grabber Setup</h1>
                <p>Welcome to the Content Grabber setup wizard. Let's configure your system to start grabbing amazing content!</p>
            </div>

            <div class="setup-steps">
                <?php foreach ($steps as $stepNumber => $stepName): ?>
                <div class="step <?php echo $stepNumber == $step ? 'active' : ''; ?>">
                    <div class="step-number"><?php echo $stepNumber; ?></div>
                    <div class="step-label"><?php echo $stepName; ?></div>
                </div>
                <?php endforeach; ?>
            </div>

            <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="setup-content">
                <?php
                // Include step content
                switch ($step) {
                    case 1:
                        include 'welcome.php';
                        break;
                    case 2:
                        include 'database.php';
                        break;
                    case 3:
                        include 'user.php';
                        break;
                    case 4:
                        include 'finish.php';
                        break;
                }
                ?>
            </div>

            <div class="setup-footer">
                <?php if ($step > 1): ?>
                <a href="?step=<?php echo $step - 1; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Previous
                </a>
                <?php else: ?>
                <div></div>
                <?php endif; ?>

                <?php if ($step < count($steps)): ?>
                <?php if ($step == 1): ?>
                <a href="?step=<?php echo $step + 1; ?>" class="btn btn-primary">
                    Next <i class="fas fa-arrow-right"></i>
                </a>
                <?php endif; ?>
                <?php elseif ($step == count($steps)): ?>
                <a href="../index.php" class="btn btn-success">
                    <i class="fas fa-check"></i> Finish Setup
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
