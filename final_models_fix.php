<?php
/**
 * Final Models Fix
 * 
 * This script performs a comprehensive fix for the models display issue.
 */

// Load configuration
require_once 'config.php';

header('Content-Type: text/plain');

echo "=== FINAL MODELS FIX ===\n\n";

try {
    $pdo = get_db_connection();
    echo "✅ Database connection established\n\n";
    
    // Step 1: Check current state
    echo "1. Checking current state...\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_models");
    $stmt->execute();
    $modelsCount = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers");
    $stmt->execute();
    $providersCount = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    $stmt->execute();
    $activeProvidersCount = $stmt->fetchColumn();
    
    echo "Total models: $modelsCount\n";
    echo "Total providers: $providersCount\n";
    echo "Active providers: $activeProvidersCount\n\n";
    
    // Step 2: Fix provider issues
    echo "2. Fixing provider issues...\n";
    
    if ($activeProvidersCount == 0 && $providersCount > 0) {
        echo "Activating all providers...\n";
        $stmt = $pdo->prepare("UPDATE ai_providers SET is_active = 1");
        $stmt->execute();
        echo "✅ All providers activated\n";
    } elseif ($providersCount == 0) {
        echo "Creating default providers...\n";
        
        $defaultProviders = [
            ['name' => 'OpenRouter', 'slug' => 'openrouter', 'description' => 'Unified API for multiple AI models'],
            ['name' => 'NOVITA AI', 'slug' => 'novita', 'description' => 'AI platform for text and image generation']
        ];
        
        foreach ($defaultProviders as $provider) {
            $stmt = $pdo->prepare("
                INSERT INTO ai_providers (name, slug, description, is_active) 
                VALUES (?, ?, ?, 1)
            ");
            $stmt->execute([$provider['name'], $provider['slug'], $provider['description']]);
            echo "✅ Created provider: {$provider['name']}\n";
        }
    }
    
    // Step 3: Fix model-provider relationships
    echo "\n3. Fixing model-provider relationships...\n";
    
    // Find orphaned models
    $stmt = $pdo->prepare("
        SELECT m.id, m.name, m.provider_id 
        FROM ai_models m 
        WHERE m.provider_id NOT IN (SELECT id FROM ai_providers)
    ");
    $stmt->execute();
    $orphanedModels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($orphanedModels)) {
        echo "Found " . count($orphanedModels) . " orphaned models\n";
        
        // Get first active provider
        $stmt = $pdo->prepare("SELECT id FROM ai_providers WHERE is_active = 1 ORDER BY id LIMIT 1");
        $stmt->execute();
        $firstProviderId = $stmt->fetchColumn();
        
        if ($firstProviderId) {
            foreach ($orphanedModels as $model) {
                $stmt = $pdo->prepare("UPDATE ai_models SET provider_id = ? WHERE id = ?");
                $stmt->execute([$firstProviderId, $model['id']]);
                echo "✅ Fixed model: {$model['name']}\n";
            }
        }
    } else {
        echo "✅ No orphaned models found\n";
    }
    
    // Step 4: Ensure models are active
    echo "\n4. Ensuring models are active...\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_models WHERE is_active = 0");
    $stmt->execute();
    $inactiveModels = $stmt->fetchColumn();
    
    if ($inactiveModels > 0) {
        echo "Activating $inactiveModels inactive models...\n";
        $stmt = $pdo->prepare("UPDATE ai_models SET is_active = 1");
        $stmt->execute();
        echo "✅ All models activated\n";
    } else {
        echo "✅ All models are already active\n";
    }
    
    // Step 5: Test the JOIN query
    echo "\n5. Testing JOIN query...\n";
    
    $stmt = $pdo->prepare("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 1
        ORDER BY p.name, m.name
    ");
    $stmt->execute();
    $visibleModels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Visible models after fix: " . count($visibleModels) . "\n";
    
    if (!empty($visibleModels)) {
        echo "✅ SUCCESS! Models that will be visible:\n";
        foreach ($visibleModels as $model) {
            echo "   • {$model['provider_name']} / {$model['name']}\n";
        }
    } else {
        echo "❌ Still no visible models. Creating test model...\n";
        
        // Get first provider
        $stmt = $pdo->prepare("SELECT id, name FROM ai_providers WHERE is_active = 1 ORDER BY id LIMIT 1");
        $stmt->execute();
        $provider = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($provider) {
            $stmt = $pdo->prepare("
                INSERT INTO ai_models (provider_id, name, model_id, type, capabilities, is_active) 
                VALUES (?, 'Test Model', 'test-model', 'text', '[]', 1)
            ");
            $stmt->execute([$provider['id']]);
            echo "✅ Created test model for provider: {$provider['name']}\n";
            
            // Test again
            $stmt = $pdo->prepare("
                SELECT m.*, p.name as provider_name
                FROM ai_models m
                JOIN ai_providers p ON m.provider_id = p.id
                WHERE p.is_active = 1
                ORDER BY p.name, m.name
            ");
            $stmt->execute();
            $testResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "Models visible after creating test model: " . count($testResults) . "\n";
        }
    }
    
    // Step 6: Final verification
    echo "\n6. Final verification...\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_models");
    $stmt->execute();
    $finalModelsCount = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    $stmt->execute();
    $finalActiveProviders = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 1
    ");
    $stmt->execute();
    $finalVisibleModels = $stmt->fetchColumn();
    
    echo "Final state:\n";
    echo "   • Total models: $finalModelsCount\n";
    echo "   • Active providers: $finalActiveProviders\n";
    echo "   • Visible models: $finalVisibleModels\n";
    
    if ($finalVisibleModels > 0) {
        echo "\n✅ SUCCESS! Models should now be visible in the interface.\n";
        echo "Go to: " . BASE_URL . "/?page=ai_settings&section=models\n";
    } else {
        echo "\n❌ Models are still not visible. Manual intervention required.\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== FIX COMPLETE ===\n";
?>
