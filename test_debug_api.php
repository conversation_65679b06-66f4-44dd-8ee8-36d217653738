<?php
/**
 * Test Debug API
 * 
 * This script tests the debug API endpoint directly.
 */

// Load configuration
require_once 'config.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== TESTING DEBUG API ===\n\n";

// Test 1: Test connection
echo "1. Testing API connection...\n";

$testData = [
    'action' => 'test_connection'
];

$response = makeApiRequest($testData);
echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";

if (!$response || !$response['success']) {
    echo "❌ API connection failed\n";
    exit;
}

echo "✅ API connection successful\n\n";

// Test 2: Test with actual model data
echo "2. Testing with model data...\n";

try {
    require_once 'includes/Database.php';
    $db = new Database();
    
    // Get first available model
    $pdo = get_db_connection();
    $stmt = $pdo->prepare("
        SELECT p.id as provider_id, p.name as provider_name, m.id as model_id, m.name as model_name
        FROM ai_providers p
        JOIN ai_models m ON p.id = m.provider_id
        WHERE p.is_active = 1 AND m.is_active = 1
        ORDER BY p.id, m.id
        LIMIT 1
    ");
    $stmt->execute();
    $testModel = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testModel) {
        echo "Using test model: {$testModel['provider_name']} / {$testModel['model_name']}\n";
        
        $testData = [
            'action' => 'send_message_direct',
            'provider_id' => $testModel['provider_id'],
            'model_id' => $testModel['model_id'],
            'message' => 'Hello, this is a test message.',
            'conversation_history' => json_encode([])
        ];
        
        echo "Sending test message...\n";
        $response = makeApiRequest($testData);
        echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
        
        if ($response && $response['success']) {
            echo "✅ Message sent successfully!\n";
            echo "AI Response: " . substr($response['response'], 0, 100) . "...\n";
            
            if (isset($response['debug_info'])) {
                echo "Debug info:\n";
                foreach ($response['debug_info'] as $key => $value) {
                    echo "   $key: $value\n";
                }
            }
        } else {
            echo "❌ Message failed\n";
            if (isset($response['error'])) {
                echo "Error: {$response['error']}\n";
            }
            if (isset($response['trace'])) {
                echo "Trace: {$response['trace']}\n";
            }
        }
    } else {
        echo "❌ No test model available\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";

/**
 * Make API request to debug endpoint
 */
function makeApiRequest($data) {
    $url = BASE_URL . '/api/ai_lab_debug.php';
    
    // Initialize cURL
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, sys_get_temp_dir() . '/ai_lab_debug_cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, sys_get_temp_dir() . '/ai_lab_debug_cookies.txt');
    
    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Raw Response: " . substr($response, 0, 500) . (strlen($response) > 500 ? '...' : '') . "\n";
    
    // Try to decode JSON
    $decoded = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "JSON Decode Error: " . json_last_error_msg() . "\n";
        return null;
    }
    
    return $decoded;
}
?>
