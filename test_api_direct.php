<?php
/**
 * Test API Direct
 * 
 * This script tests the AI Lab API directly to see what's happening.
 */

// Load configuration
require_once 'config.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== TESTING API DIRECT ===\n\n";

try {
    require_once 'includes/Database.php';
    $db = new Database();
    $pdo = get_db_connection();
    
    // Get test data
    $stmt = $pdo->prepare("
        SELECT p.id as provider_id, p.name as provider_name, p.slug, m.id as model_id, m.name as model_name
        FROM ai_providers p
        JOIN ai_models m ON p.id = m.provider_id
        WHERE p.is_active = 1 AND m.is_active = 1
        ORDER BY p.id, m.id
        LIMIT 1
    ");
    $stmt->execute();
    $testData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testData) {
        echo "❌ No test data available\n";
        exit;
    }
    
    echo "Test data:\n";
    echo "Provider: {$testData['provider_name']} (ID: {$testData['provider_id']}, Slug: {$testData['slug']})\n";
    echo "Model: {$testData['model_name']} (ID: {$testData['model_id']})\n\n";
    
    // Test 1: Test connection endpoint
    echo "1. Testing connection endpoint...\n";
    $response1 = makeApiRequest([
        'action' => 'test_connection'
    ]);
    
    if ($response1 && $response1['success']) {
        echo "✅ Connection test successful\n";
        echo "User ID: {$response1['user_id']}\n";
        echo "Timestamp: {$response1['timestamp']}\n\n";
    } else {
        echo "❌ Connection test failed\n";
        if ($response1) {
            echo "Error: " . ($response1['error'] ?? 'Unknown error') . "\n";
        }
        echo "\n";
    }
    
    // Test 2: Test AI provider creation directly
    echo "2. Testing AI provider creation...\n";
    
    try {
        require_once 'includes/AIProvider.php';
        $aiProvider = AIProvider::create($db, $testData['slug']);
        echo "✅ AI Provider created successfully\n";
        
        // Test simple generation
        $testResponse = $aiProvider->generateText('Say hello', [
            'model_id' => $testData['model_id'],
            'max_tokens' => 50
        ]);
        
        echo "✅ Direct AI test successful!\n";
        echo "Response: " . substr($testResponse['text'], 0, 100) . "...\n";
        echo "Tokens: {$testResponse['tokens_used']}\n\n";
        
    } catch (Exception $e) {
        echo "❌ Direct AI test failed: " . $e->getMessage() . "\n\n";
    }
    
    // Test 3: Test send_message_direct endpoint
    echo "3. Testing send_message_direct endpoint...\n";
    $response3 = makeApiRequest([
        'action' => 'send_message_direct',
        'provider_id' => $testData['provider_id'],
        'model_id' => $testData['model_id'],
        'message' => 'Hello, this is a test message from the API test script.',
        'conversation_history' => json_encode([])
    ]);
    
    if ($response3 && $response3['success']) {
        echo "✅ send_message_direct test successful!\n";
        echo "Response: " . substr($response3['response'], 0, 100) . "...\n";
        echo "Tokens: {$response3['tokens_used']}\n";
        echo "Time: {$response3['execution_time']}s\n";
        echo "Model: {$response3['model_used']}\n\n";
    } else {
        echo "❌ send_message_direct test failed\n";
        if ($response3) {
            echo "Error: " . ($response3['error'] ?? 'Unknown error') . "\n";
        }
        echo "\n";
    }
    
    // Test 4: Test with conversation history
    echo "4. Testing with conversation history...\n";
    $conversationHistory = [
        ['role' => 'user', 'content' => 'My name is John.'],
        ['role' => 'assistant', 'content' => 'Hello John! Nice to meet you.'],
    ];
    
    $response4 = makeApiRequest([
        'action' => 'send_message_direct',
        'provider_id' => $testData['provider_id'],
        'model_id' => $testData['model_id'],
        'message' => 'What is my name?',
        'conversation_history' => json_encode($conversationHistory)
    ]);
    
    if ($response4 && $response4['success']) {
        echo "✅ Conversation history test successful!\n";
        echo "Response: " . substr($response4['response'], 0, 100) . "...\n";
        
        if (stripos($response4['response'], 'john') !== false) {
            echo "✅ AI correctly remembered the name!\n";
        } else {
            echo "⚠️  AI may not have used conversation history\n";
        }
    } else {
        echo "❌ Conversation history test failed\n";
        if ($response4) {
            echo "Error: " . ($response4['error'] ?? 'Unknown error') . "\n";
        }
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "Connection test: " . ($response1 && $response1['success'] ? '✅' : '❌') . "\n";
    echo "Direct message test: " . ($response3 && $response3['success'] ? '✅' : '❌') . "\n";
    echo "Conversation history test: " . ($response4 && $response4['success'] ? '✅' : '❌') . "\n";
    
    if ($response3 && $response3['success']) {
        echo "\n🎉 API is working correctly!\n";
        echo "The issue might be in the frontend JavaScript or browser.\n";
        echo "\nTry these steps:\n";
        echo "1. Clear browser cache and cookies\n";
        echo "2. Open browser dev tools (F12) and check Console tab\n";
        echo "3. Try the AI Lab again and watch for JavaScript errors\n";
    } else {
        echo "\n❌ API has issues that need to be resolved.\n";
    }
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";

/**
 * Make API request
 */
function makeApiRequest($data) {
    $url = BASE_URL . '/api/ai_lab.php';
    
    // Initialize cURL
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, sys_get_temp_dir() . '/api_test_cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, sys_get_temp_dir() . '/api_test_cookies.txt');
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    
    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Raw Response: " . substr($response, 0, 200) . (strlen($response) > 200 ? '...' : '') . "\n";
    
    // Try to decode JSON
    $decoded = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "JSON Decode Error: " . json_last_error_msg() . "\n";
        echo "Full response: $response\n";
        return null;
    }
    
    return $decoded;
}
?>
