<?php
/**
 * Test Direct PDO
 * 
 * This script tests database queries using direct PDO to bypass the Database class.
 */

// Load configuration
require_once 'config.php';

header('Content-Type: text/plain');

echo "=== TESTING DIRECT PDO ===\n\n";

try {
    // Get direct PDO connection
    $pdo = get_db_connection();
    echo "✅ PDO connection established\n\n";
    
    // Test 1: Basic counts
    echo "1. Basic counts:\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_models");
    $stmt->execute();
    $modelsCount = $stmt->fetchColumn();
    echo "Total models: $modelsCount\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers");
    $stmt->execute();
    $providersCount = $stmt->fetchColumn();
    echo "Total providers: $providersCount\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    $stmt->execute();
    $activeProvidersCount = $stmt->fetchColumn();
    echo "Active providers: $activeProvidersCount\n\n";
    
    // Test 2: Show all models
    echo "2. All models in database:\n";
    $stmt = $pdo->prepare("SELECT id, name, model_id, provider_id, is_active FROM ai_models ORDER BY id");
    $stmt->execute();
    $allModels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allModels as $model) {
        $status = $model['is_active'] ? 'ACTIVE' : 'INACTIVE';
        echo "   ID: {$model['id']}, Name: {$model['name']}, Model ID: {$model['model_id']}, Provider ID: {$model['provider_id']}, Status: $status\n";
    }
    echo "\n";
    
    // Test 3: Show all providers
    echo "3. All providers in database:\n";
    $stmt = $pdo->prepare("SELECT id, name, slug, is_active FROM ai_providers ORDER BY id");
    $stmt->execute();
    $allProviders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allProviders as $provider) {
        $status = $provider['is_active'] ? 'ACTIVE' : 'INACTIVE';
        echo "   ID: {$provider['id']}, Name: {$provider['name']}, Slug: {$provider['slug']}, Status: $status\n";
    }
    echo "\n";
    
    // Test 4: Test the JOIN query directly
    echo "4. Testing JOIN query (active providers only):\n";
    $sql = "
        SELECT m.*, p.name as provider_name, p.is_active as provider_active
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 1
        ORDER BY p.name, m.name
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $joinResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "JOIN query result count: " . count($joinResults) . "\n";
    
    if (!empty($joinResults)) {
        echo "JOIN query results:\n";
        foreach ($joinResults as $model) {
            echo "   • {$model['provider_name']} / {$model['name']} ({$model['model_id']})\n";
        }
    } else {
        echo "❌ JOIN query returned no results\n";
        
        // Test with all providers (including inactive)
        echo "\n5. Testing JOIN query (all providers):\n";
        $sql2 = "
            SELECT m.*, p.name as provider_name, p.is_active as provider_active
            FROM ai_models m
            JOIN ai_providers p ON m.provider_id = p.id
            ORDER BY p.name, m.name
        ";
        
        $stmt = $pdo->prepare($sql2);
        $stmt->execute();
        $joinResults2 = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "JOIN query (all providers) result count: " . count($joinResults2) . "\n";
        
        if (!empty($joinResults2)) {
            echo "JOIN query (all providers) results:\n";
            foreach ($joinResults2 as $model) {
                $providerStatus = $model['provider_active'] ? 'ACTIVE' : 'INACTIVE';
                echo "   • {$model['provider_name']} ($providerStatus) / {$model['name']} ({$model['model_id']})\n";
            }
        } else {
            echo "❌ JOIN query with all providers also returned no results\n";
            
            // Check for data integrity issues
            echo "\n6. Checking data integrity:\n";
            
            // Find models with invalid provider_id
            $stmt = $pdo->prepare("
                SELECT m.id, m.name, m.provider_id 
                FROM ai_models m 
                WHERE m.provider_id NOT IN (SELECT id FROM ai_providers)
            ");
            $stmt->execute();
            $orphanedModels = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($orphanedModels)) {
                echo "❌ Found orphaned models (invalid provider_id):\n";
                foreach ($orphanedModels as $model) {
                    echo "   • Model ID: {$model['id']}, Name: {$model['name']}, Invalid Provider ID: {$model['provider_id']}\n";
                }
            } else {
                echo "✅ No orphaned models found\n";
            }
        }
    }
    
    // Test 5: Create a simple test model if none exist
    if (empty($joinResults) && empty($joinResults2)) {
        echo "\n7. Creating test data...\n";
        
        // Ensure we have at least one active provider
        $stmt = $pdo->prepare("SELECT id FROM ai_providers WHERE is_active = 1 LIMIT 1");
        $stmt->execute();
        $activeProvider = $stmt->fetchColumn();
        
        if (!$activeProvider) {
            echo "No active provider found. Creating test provider...\n";
            $stmt = $pdo->prepare("
                INSERT INTO ai_providers (name, slug, description, is_active) 
                VALUES ('Test Provider', 'test', 'Test provider for debugging', 1)
            ");
            $stmt->execute();
            $activeProvider = $pdo->lastInsertId();
            echo "Created test provider with ID: $activeProvider\n";
        }
        
        // Create a test model
        echo "Creating test model...\n";
        $stmt = $pdo->prepare("
            INSERT INTO ai_models (provider_id, name, model_id, type, capabilities, is_active) 
            VALUES (?, 'Test Model', 'test-model', 'text', '[]', 1)
        ");
        $stmt->execute([$activeProvider]);
        $testModelId = $pdo->lastInsertId();
        echo "Created test model with ID: $testModelId\n";
        
        // Test the JOIN query again
        echo "Testing JOIN query with test data...\n";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $testResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "JOIN query with test data result count: " . count($testResults) . "\n";
        if (!empty($testResults)) {
            echo "✅ JOIN query now works with test data!\n";
            foreach ($testResults as $model) {
                echo "   • {$model['provider_name']} / {$model['name']}\n";
            }
        }
    }
    
    // Final summary
    echo "\n=== SUMMARY ===\n";
    echo "Models in database: $modelsCount\n";
    echo "Providers in database: $providersCount\n";
    echo "Active providers: $activeProvidersCount\n";
    echo "Models visible with JOIN (active providers): " . count($joinResults ?? []) . "\n";
    echo "Models visible with JOIN (all providers): " . count($joinResults2 ?? []) . "\n";
    
    if (!empty($joinResults) || !empty($joinResults2)) {
        echo "✅ CONCLUSION: Models should be visible in the interface\n";
    } else {
        echo "❌ CONCLUSION: No models will be visible - data integrity issue\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
