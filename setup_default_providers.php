<?php
/**
 * Setup Default AI Providers
 * 
 * This script adds default AI providers to the database including OpenRouter and others.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Default providers to add
$defaultProviders = [
    [
        'name' => 'OpenRouter',
        'slug' => 'openrouter',
        'description' => 'Access to multiple AI models through a unified API including GPT-4, Claude, Gemini, and more.',
        'website' => 'https://openrouter.ai',
        'api_base_url' => 'https://openrouter.ai/api/v1',
        'auth_type' => 'bearer',
        'rate_limit_per_minute' => 200,
        'supports_streaming' => 1,
        'is_active' => 1
    ],
    [
        'name' => 'NOVITA AI',
        'slug' => 'novita',
        'description' => 'AI platform providing text and image generation capabilities.',
        'website' => 'https://novita.ai',
        'api_base_url' => 'https://api.novita.ai/v1',
        'auth_type' => 'bearer',
        'rate_limit_per_minute' => 100,
        'supports_streaming' => 0,
        'is_active' => 1
    ],
    [
        'name' => 'OpenAI',
        'slug' => 'openai',
        'description' => 'Leading AI research company providing GPT models and DALL-E image generation.',
        'website' => 'https://openai.com',
        'api_base_url' => 'https://api.openai.com/v1',
        'auth_type' => 'bearer',
        'rate_limit_per_minute' => 60,
        'supports_streaming' => 1,
        'is_active' => 1
    ],
    [
        'name' => 'Anthropic',
        'slug' => 'anthropic',
        'description' => 'AI safety company creating helpful, harmless, and honest AI systems like Claude.',
        'website' => 'https://anthropic.com',
        'api_base_url' => 'https://api.anthropic.com/v1',
        'auth_type' => 'api_key',
        'rate_limit_per_minute' => 50,
        'supports_streaming' => 1,
        'is_active' => 1
    ],
    [
        'name' => 'Google AI',
        'slug' => 'google',
        'description' => 'Google\'s AI platform providing access to Gemini models and other AI services.',
        'website' => 'https://ai.google.dev',
        'api_base_url' => 'https://generativelanguage.googleapis.com/v1',
        'auth_type' => 'api_key',
        'rate_limit_per_minute' => 60,
        'supports_streaming' => 1,
        'is_active' => 1
    ]
];

// Default models for OpenRouter
$openrouterModels = [
    [
        'name' => 'GPT-4 Turbo',
        'model_id' => 'openai/gpt-4-turbo',
        'type' => 'text',
        'capabilities' => ['text_generation', 'conversation', 'analysis'],
        'system_prompt' => 'You are a helpful AI assistant.',
        'max_tokens' => 4096,
        'temperature' => 0.7
    ],
    [
        'name' => 'GPT-3.5 Turbo',
        'model_id' => 'openai/gpt-3.5-turbo',
        'type' => 'text',
        'capabilities' => ['text_generation', 'conversation'],
        'system_prompt' => 'You are a helpful AI assistant.',
        'max_tokens' => 4096,
        'temperature' => 0.7
    ],
    [
        'name' => 'Claude 3 Opus',
        'model_id' => 'anthropic/claude-3-opus',
        'type' => 'text',
        'capabilities' => ['text_generation', 'conversation', 'analysis', 'reasoning'],
        'system_prompt' => 'You are Claude, an AI assistant created by Anthropic.',
        'max_tokens' => 4096,
        'temperature' => 0.7
    ],
    [
        'name' => 'Claude 3 Sonnet',
        'model_id' => 'anthropic/claude-3-sonnet',
        'type' => 'text',
        'capabilities' => ['text_generation', 'conversation', 'analysis'],
        'system_prompt' => 'You are Claude, an AI assistant created by Anthropic.',
        'max_tokens' => 4096,
        'temperature' => 0.7
    ],
    [
        'name' => 'Gemini Pro',
        'model_id' => 'google/gemini-pro',
        'type' => 'text',
        'capabilities' => ['text_generation', 'conversation', 'analysis'],
        'system_prompt' => 'You are a helpful AI assistant.',
        'max_tokens' => 4096,
        'temperature' => 0.7
    ],
    [
        'name' => 'Llama 2 70B',
        'model_id' => 'meta-llama/llama-2-70b-chat',
        'type' => 'text',
        'capabilities' => ['text_generation', 'conversation'],
        'system_prompt' => 'You are a helpful AI assistant.',
        'max_tokens' => 4096,
        'temperature' => 0.7
    ]
];

try {
    echo "Setting up default AI providers...\n";
    
    // Check if providers table exists
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    if (empty($tableExists)) {
        echo "Error: AI providers table does not exist. Please run the database setup first.\n";
        exit(1);
    }
    
    // Add providers
    foreach ($defaultProviders as $providerData) {
        // Check if provider already exists
        $existingProvider = $db->getRow("SELECT id FROM ai_providers WHERE slug = ?", [$providerData['slug']]);
        
        if ($existingProvider) {
            echo "Provider '{$providerData['name']}' already exists, updating...\n";
            
            // Update existing provider
            $db->update('ai_providers', $providerData, 'id = ?', [$existingProvider['id']]);
            $providerId = $existingProvider['id'];
        } else {
            echo "Adding provider '{$providerData['name']}'...\n";
            
            // Insert new provider
            $providerId = $db->insert('ai_providers', $providerData);
        }
        
        // Add models for OpenRouter
        if ($providerData['slug'] === 'openrouter') {
            echo "Adding OpenRouter models...\n";
            
            foreach ($openrouterModels as $modelData) {
                // Check if model already exists
                $existingModel = $db->getRow("SELECT id FROM ai_models WHERE provider_id = ? AND model_id = ?", [$providerId, $modelData['model_id']]);
                
                if ($existingModel) {
                    echo "  Model '{$modelData['name']}' already exists, updating...\n";
                    
                    // Update existing model
                    $updateData = $modelData;
                    $updateData['capabilities'] = json_encode($modelData['capabilities']);
                    $db->update('ai_models', $updateData, 'id = ?', [$existingModel['id']]);
                } else {
                    echo "  Adding model '{$modelData['name']}'...\n";
                    
                    // Insert new model
                    $insertData = $modelData;
                    $insertData['provider_id'] = $providerId;
                    $insertData['capabilities'] = json_encode($modelData['capabilities']);
                    $insertData['is_active'] = 1;
                    
                    $db->insert('ai_models', $insertData);
                }
            }
        }
    }
    
    echo "\nDefault providers setup completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Go to AI Settings > API Keys to add your API keys\n";
    echo "2. Configure models for each provider\n";
    echo "3. Create workflows to use AI processing\n";
    echo "4. Test providers in the AI Lab\n";
    
} catch (Exception $e) {
    echo "Error setting up providers: " . $e->getMessage() . "\n";
    exit(1);
}
?>
