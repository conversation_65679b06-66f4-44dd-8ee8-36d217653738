<?php
/**
 * Final Models Test
 * 
 * This script tests if the models are now visible and working correctly.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

header('Content-Type: text/plain');

echo "=== FINAL MODELS TEST ===\n\n";

try {
    // Test the exact same logic as the updated template
    echo "1. Testing updated template logic...\n";
    
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    if (!empty($tableExists)) {
        echo "✅ ai_providers table exists\n";
        
        $providers = $db->query("SELECT * FROM ai_providers ORDER BY name");
        echo "✅ Found " . count($providers) . " providers\n";
        
        $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");
        if (!empty($modelsTableExists)) {
            echo "✅ ai_models table exists\n";
            
            // Test the new robust query logic
            try {
                // First try: Active providers only
                $models = $db->query("
                    SELECT m.*, p.name as provider_name
                    FROM ai_models m
                    JOIN ai_providers p ON m.provider_id = p.id
                    WHERE p.is_active = 1
                    ORDER BY p.name, m.name
                ");
                
                echo "First query (active providers): " . count($models) . " models\n";
                
                // If no models found with active providers, try with all providers
                if (empty($models)) {
                    $models = $db->query("
                        SELECT m.*, p.name as provider_name
                        FROM ai_models m
                        JOIN ai_providers p ON m.provider_id = p.id
                        ORDER BY p.name, m.name
                    ");
                    echo "Second query (all providers): " . count($models) . " models\n";
                }
                
                // If still no models, check for orphaned models
                if (empty($models)) {
                    $orphanedModels = $db->query("
                        SELECT m.*, 'Unknown Provider' as provider_name
                        FROM ai_models m
                        LEFT JOIN ai_providers p ON m.provider_id = p.id
                        WHERE p.id IS NULL
                        ORDER BY m.name
                    ");
                    
                    if (!empty($orphanedModels)) {
                        $models = $orphanedModels;
                        echo "Third query (orphaned models): " . count($models) . " models\n";
                    }
                }
                
                if (!empty($models)) {
                    echo "✅ SUCCESS! Models found and will be displayed:\n";
                    foreach ($models as $model) {
                        $status = $model['is_active'] ? 'ACTIVE' : 'INACTIVE';
                        echo "   • {$model['provider_name']} / {$model['name']} ({$model['model_id']}) - $status\n";
                    }
                } else {
                    echo "❌ No models found with any query\n";
                }
                
            } catch (Exception $e) {
                echo "❌ Query failed: " . $e->getMessage() . "\n";
                
                // Test fallback query
                $fallbackModels = $db->query("SELECT *, 'Unknown Provider' as provider_name FROM ai_models ORDER BY name");
                echo "Fallback query: " . count($fallbackModels) . " models\n";
            }
        } else {
            echo "❌ ai_models table does not exist\n";
        }
    } else {
        echo "❌ ai_providers table does not exist\n";
    }
    
    // Test AI Lab compatibility
    echo "\n2. Testing AI Lab compatibility...\n";
    $labCompatibleModels = $db->query("
        SELECT m.id, m.name, p.name as provider_name, p.slug as provider_slug
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE m.is_active = 1 AND p.is_active = 1
        ORDER BY p.name, m.name
    ");
    
    echo "Models available for AI Lab: " . count($labCompatibleModels) . "\n";
    foreach ($labCompatibleModels as $model) {
        echo "   • {$model['provider_name']} / {$model['name']} (ID: {$model['id']})\n";
    }
    
    // Summary and recommendations
    echo "\n3. Summary and recommendations...\n";
    
    $totalModels = $db->getValue("SELECT COUNT(*) FROM ai_models");
    $totalProviders = $db->getValue("SELECT COUNT(*) FROM ai_providers");
    $activeProviders = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    $activeModels = $db->getValue("SELECT COUNT(*) FROM ai_models WHERE is_active = 1");
    
    echo "Database summary:\n";
    echo "   • Total models: $totalModels\n";
    echo "   • Active models: $activeModels\n";
    echo "   • Total providers: $totalProviders\n";
    echo "   • Active providers: $activeProviders\n";
    
    if ($totalModels > 0 && $activeProviders > 0) {
        echo "\n✅ SYSTEM STATUS: READY\n";
        echo "Your AI system is properly configured!\n\n";
        
        echo "Next steps:\n";
        echo "1. Add API keys in AI Settings > API Keys\n";
        echo "2. Test models in AI Lab\n";
        echo "3. Create workflows in AI Settings > Workflows\n";
        echo "4. Start processing content with AI\n";
    } else {
        echo "\n⚠️  SYSTEM STATUS: NEEDS ATTENTION\n";
        
        if ($totalModels == 0) {
            echo "• No models found - Add models in AI Settings > Models\n";
        }
        
        if ($activeProviders == 0) {
            echo "• No active providers - Run setup_default_providers.php\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
