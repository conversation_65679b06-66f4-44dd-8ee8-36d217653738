<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Lab Frontend Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>AI Lab Frontend Test</h1>
        <p>This page tests the exact same AJAX calls as the AI Lab to isolate the redirect issue.</p>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="provider-id" class="form-label">Provider ID</label>
                            <input type="number" class="form-control" id="provider-id" value="1">
                        </div>
                        <div class="mb-3">
                            <label for="model-id" class="form-label">Model ID</label>
                            <input type="number" class="form-control" id="model-id" value="1">
                        </div>
                        <div class="mb-3">
                            <label for="test-message" class="form-label">Test Message</label>
                            <input type="text" class="form-control" id="test-message" value="Hello, this is a test message.">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="testMinimal()">Test Minimal API</button>
                        <button type="button" class="btn btn-info" onclick="testConnection()">Test Connection</button>
                        <button type="button" class="btn btn-success" onclick="sendTestMessage()">Send Test Message</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Response Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="response-log" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            Ready to test...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('response-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testMinimal() {
            log('Testing minimal API...');

            fetch('/sc/api/test_minimal.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'test_minimal'
                })
            })
            .then(response => {
                log(`Response status: ${response.status}`);
                log(`Response URL: ${response.url}`);
                log(`Response redirected: ${response.redirected}`);

                return response.text().then(text => {
                    log(`Raw response: ${text.substring(0, 200)}...`);

                    // Check if response looks like HTML (redirect)
                    if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
                        log('❌ REDIRECT DETECTED! Response is HTML, not JSON');
                        return { success: false, error: 'Server redirect detected' };
                    }

                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        log(`JSON parse error: ${e.message}`);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                log(`Parsed data: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    log('✅ Minimal API test successful!');
                } else {
                    log(`❌ Minimal API test failed: ${data.error}`);
                }
            })
            .catch(error => {
                log(`❌ Fetch error: ${error.message}`);
            });
        }

        function testConnection() {
            log('Testing connection...');

            fetch('/sc/api/ai_lab.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'test_connection'
                })
            })
            .then(response => {
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify([...response.headers])}`);

                return response.text().then(text => {
                    log(`Raw response: ${text}`);

                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        log(`JSON parse error: ${e.message}`);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                log(`Parsed data: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    log('✅ Connection test successful!');
                } else {
                    log(`❌ Connection test failed: ${data.error}`);
                }
            })
            .catch(error => {
                log(`❌ Fetch error: ${error.message}`);
            });
        }

        function sendTestMessage() {
            const providerId = document.getElementById('provider-id').value;
            const modelId = document.getElementById('model-id').value;
            const message = document.getElementById('test-message').value;

            log(`Sending message: "${message}"`);
            log(`Provider ID: ${providerId}, Model ID: ${modelId}`);

            const requestData = {
                action: 'send_message_direct',
                provider_id: providerId,
                model_id: modelId,
                message: message,
                conversation_history: JSON.stringify([])
            };

            log(`Request data: ${JSON.stringify(requestData)}`);

            fetch('/sc/api/ai_lab.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(requestData)
            })
            .then(response => {
                log(`Response status: ${response.status}`);
                log(`Response URL: ${response.url}`);
                log(`Response redirected: ${response.redirected}`);

                return response.text().then(text => {
                    log(`Raw response length: ${text.length}`);
                    log(`Raw response start: ${text.substring(0, 200)}...`);

                    // Check if response looks like HTML (redirect)
                    if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
                        log('❌ REDIRECT DETECTED! Response is HTML, not JSON');
                        log('This means the server is redirecting instead of returning API response');
                        return { success: false, error: 'Server redirect detected' };
                    }

                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        log(`JSON parse error: ${e.message}`);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                log(`Parsed data: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    log('✅ Message sent successfully!');
                    log(`AI Response: ${data.response.substring(0, 100)}...`);
                    log(`Tokens used: ${data.tokens_used}`);
                    log(`Execution time: ${data.execution_time}s`);
                } else {
                    log(`❌ Message failed: ${data.error}`);
                }
            })
            .catch(error => {
                log(`❌ Fetch error: ${error.message}`);
            });
        }

        // Auto-test minimal API on page load
        window.addEventListener('load', function() {
            log('Page loaded. Auto-testing minimal API...');
            setTimeout(testMinimal, 1000);
        });
    </script>
</body>
</html>
