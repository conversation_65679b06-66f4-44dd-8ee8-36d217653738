<?php
/**
 * AI Lab AJAX API
 *
 * This script handles AJAX requests for the AI Lab functionality.
 */

// Load configuration
require_once '../config.php';
require_once '../includes/Database.php';
require_once '../includes/AILabManager.php';

// Start session
session_start();

// Set content type
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Initialize database and lab manager
try {
    $db = new Database();
    $labManager = new AILabManager($db, $_SESSION['user_id']);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Get action from POST data
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'create_conversation':
            $providerId = (int)($_POST['provider_id'] ?? 0);
            $modelId = (int)($_POST['model_id'] ?? 0);
            $title = $_POST['title'] ?? null;

            if (!$providerId || !$modelId) {
                throw new Exception('Provider ID and Model ID are required');
            }

            $conversationId = $labManager->createConversation($providerId, $modelId, $title);
            echo json_encode(['success' => true, 'conversation_id' => $conversationId]);
            break;

        case 'send_message':
            $conversationId = (int)($_POST['conversation_id'] ?? 0);
            $message = $_POST['message'] ?? '';

            if (!$conversationId) {
                throw new Exception('Conversation ID is required');
            }

            if (empty($message)) {
                throw new Exception('Message cannot be empty');
            }

            $result = $labManager->sendMessage($conversationId, $message);
            echo json_encode($result);
            break;

        case 'send_message_direct':
            $providerId = (int)($_POST['provider_id'] ?? 0);
            $modelId = (int)($_POST['model_id'] ?? 0);
            $message = $_POST['message'] ?? '';
            $conversationHistory = json_decode($_POST['conversation_history'] ?? '[]', true);

            if (!$providerId || !$modelId) {
                throw new Exception('Provider ID and Model ID are required');
            }

            if (empty($message)) {
                throw new Exception('Message cannot be empty');
            }

            // Load AI provider
            require_once '../includes/AIProvider.php';
            $provider = $db->getRow("SELECT slug FROM ai_providers WHERE id = ? AND is_active = 1", [$providerId]);
            if (!$provider) {
                throw new Exception('Provider not found or not active');
            }

            $aiProvider = AIProvider::create($db, $provider['slug']);

            // Generate response
            $response = $aiProvider->generateText($message, [
                'model_id' => $modelId,
                'conversation_history' => $conversationHistory
            ]);

            echo json_encode([
                'success' => true,
                'response' => $response['text'],
                'tokens_used' => $response['tokens_used'] ?? 0,
                'execution_time' => $response['execution_time'] ?? 0,
                'model_used' => $response['model'] ?? 'Unknown'
            ]);
            break;

        case 'delete_conversation':
            $conversationId = (int)($_POST['conversation_id'] ?? 0);

            if (!$conversationId) {
                throw new Exception('Conversation ID is required');
            }

            $labManager->deleteConversation($conversationId);
            echo json_encode(['success' => true]);
            break;

        case 'update_title':
            $conversationId = (int)($_POST['conversation_id'] ?? 0);
            $title = $_POST['title'] ?? '';

            if (!$conversationId) {
                throw new Exception('Conversation ID is required');
            }

            if (empty($title)) {
                throw new Exception('Title cannot be empty');
            }

            $labManager->updateConversationTitle($conversationId, $title);
            echo json_encode(['success' => true]);
            break;

        case 'get_conversations':
            $conversations = $labManager->getUserConversations();
            echo json_encode(['success' => true, 'conversations' => $conversations]);
            break;

        case 'get_conversation':
            $conversationId = (int)($_POST['conversation_id'] ?? 0);

            if (!$conversationId) {
                throw new Exception('Conversation ID is required');
            }

            $conversation = $labManager->getConversation($conversationId);
            if (!$conversation) {
                throw new Exception('Conversation not found');
            }

            $messages = $labManager->getConversationMessages($conversationId);
            echo json_encode([
                'success' => true,
                'conversation' => $conversation,
                'messages' => $messages
            ]);
            break;

        case 'get_statistics':
            $stats = $labManager->getLabStatistics();
            echo json_encode(['success' => true, 'statistics' => $stats]);
            break;

        case 'test_connection':
            // Simple test to verify the API is working
            echo json_encode([
                'success' => true,
                'message' => 'AI Lab API is working',
                'user_id' => $_SESSION['user_id'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
