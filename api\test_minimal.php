<?php
/**
 * Minimal Test API
 * 
 * This is a minimal API endpoint to test if the basic setup works.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

// Load configuration
require_once '../config.php';

// Start session
session_start();

// Check for any unexpected output
$unexpectedOutput = ob_get_contents();
ob_end_clean();

// Set content type
header('Content-Type: application/json');

// Log unexpected output if any
if (!empty($unexpectedOutput)) {
    echo json_encode([
        'success' => false, 
        'error' => 'Unexpected output detected',
        'unexpected_output' => $unexpectedOutput
    ]);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized - no user_id in session']);
    exit;
}

// Get action from POST data
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'test_minimal':
            echo json_encode([
                'success' => true,
                'message' => 'Minimal API test successful',
                'user_id' => $_SESSION['user_id'],
                'timestamp' => date('Y-m-d H:i:s'),
                'session_id' => session_id(),
                'post_data' => $_POST,
                'server_info' => [
                    'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
                    'REQUEST_URI' => $_SERVER['REQUEST_URI'],
                    'HTTP_HOST' => $_SERVER['HTTP_HOST']
                ]
            ]);
            break;
            
        case 'test_database':
            require_once '../includes/Database.php';
            $db = new Database();
            
            // Simple database test
            $result = $db->query("SELECT 1 as test");
            
            echo json_encode([
                'success' => true,
                'message' => 'Database test successful',
                'database_result' => $result
            ]);
            break;
            
        case 'test_ai_provider':
            require_once '../includes/Database.php';
            $db = new Database();
            
            // Get first provider
            $provider = $db->getRow("SELECT * FROM ai_providers WHERE is_active = 1 LIMIT 1");
            
            if (!$provider) {
                throw new Exception('No active providers found');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'AI provider test successful',
                'provider' => $provider
            ]);
            break;
            
        default:
            throw new Exception('Invalid action: ' . $action);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
