<?php
/**
 * Test OpenRouter Chat Integration
 * 
 * This script tests the OpenRouter AI provider with proper chat functionality.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';
require_once 'includes/AIProvider.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== TESTING OPENROUTER CHAT INTEGRATION ===\n\n";

try {
    $db = new Database();
    
    // Test 1: Check if OpenRouter provider exists
    echo "1. Checking OpenRouter provider...\n";
    
    $provider = $db->getRow("SELECT * FROM ai_providers WHERE slug = 'openrouter' AND is_active = 1");
    if (!$provider) {
        echo "❌ OpenRouter provider not found or not active\n";
        echo "Please run setup_default_providers.php first\n";
        exit;
    }
    
    echo "✅ OpenRouter provider found: {$provider['name']} (ID: {$provider['id']})\n\n";
    
    // Test 2: Check for OpenRouter models
    echo "2. Checking OpenRouter models...\n";
    
    $models = $db->query("SELECT * FROM ai_models WHERE provider_id = ? AND is_active = 1", [$provider['id']]);
    if (empty($models)) {
        echo "❌ No OpenRouter models found\n";
        echo "Please add some OpenRouter models in AI Settings > Models\n";
        exit;
    }
    
    echo "✅ Found " . count($models) . " OpenRouter models:\n";
    foreach ($models as $model) {
        echo "   • {$model['name']} ({$model['model_id']})\n";
    }
    echo "\n";
    
    // Test 3: Check API key
    echo "3. Checking API key...\n";
    
    $apiKey = $db->getRow("SELECT * FROM ai_api_keys WHERE provider_id = ? AND is_active = 1", [$provider['id']]);
    if (!$apiKey) {
        echo "❌ No API key found for OpenRouter\n";
        echo "Please add your OpenRouter API key in AI Settings > API Keys\n";
        exit;
    }
    
    echo "✅ API key found (Key ID: {$apiKey['id']})\n\n";
    
    // Test 4: Create AI provider instance
    echo "4. Creating AI provider instance...\n";
    
    $aiProvider = AIProvider::create($db, 'openrouter');
    echo "✅ OpenRouter AI provider created successfully\n\n";
    
    // Test 5: Test simple text generation
    echo "5. Testing simple text generation...\n";
    
    $testModel = $models[0]; // Use first available model
    echo "Using model: {$testModel['name']} ({$testModel['model_id']})\n";
    
    $simplePrompt = "Hello! Please respond with a brief greeting.";
    
    try {
        $response = $aiProvider->generateText($simplePrompt, [
            'model_id' => $testModel['id'],
            'max_tokens' => 100
        ]);
        
        echo "✅ Simple text generation successful!\n";
        echo "Response: " . substr($response['text'], 0, 100) . "...\n";
        echo "Tokens used: {$response['tokens_used']}\n";
        echo "Execution time: " . number_format($response['execution_time'], 2) . "s\n\n";
        
    } catch (Exception $e) {
        echo "❌ Simple text generation failed: " . $e->getMessage() . "\n";
        exit;
    }
    
    // Test 6: Test conversation history
    echo "6. Testing conversation history...\n";
    
    $conversationHistory = [
        ['role' => 'user', 'content' => 'Hello, my name is John.'],
        ['role' => 'assistant', 'content' => 'Hello John! Nice to meet you.'],
        ['role' => 'user', 'content' => 'What is my name?']
    ];
    
    try {
        $response = $aiProvider->generateText('What is my name?', [
            'model_id' => $testModel['id'],
            'conversation_history' => $conversationHistory,
            'max_tokens' => 50
        ]);
        
        echo "✅ Conversation history test successful!\n";
        echo "Response: {$response['text']}\n";
        echo "Tokens used: {$response['tokens_used']}\n\n";
        
        // Check if the AI remembered the name
        if (stripos($response['text'], 'john') !== false) {
            echo "✅ AI correctly remembered the name from conversation history!\n";
        } else {
            echo "⚠️  AI may not have used conversation history properly\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Conversation history test failed: " . $e->getMessage() . "\n";
    }
    
    // Test 7: Test connection method
    echo "\n7. Testing connection method...\n";
    
    try {
        $connectionTest = $aiProvider->testConnection($testModel['model_id']);
        
        if ($connectionTest['success']) {
            echo "✅ Connection test successful!\n";
            echo "Response: {$connectionTest['response']}\n";
            echo "Tokens used: {$connectionTest['tokens_used']}\n";
        } else {
            echo "❌ Connection test failed: {$connectionTest['message']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Connection test error: " . $e->getMessage() . "\n";
    }
    
    // Test 8: Test with different models (if available)
    if (count($models) > 1) {
        echo "\n8. Testing with different models...\n";
        
        foreach (array_slice($models, 0, 3) as $model) {
            echo "Testing model: {$model['name']}\n";
            
            try {
                $response = $aiProvider->generateText("Say hello in a creative way.", [
                    'model_id' => $model['id'],
                    'max_tokens' => 50
                ]);
                
                echo "✅ {$model['name']}: " . substr($response['text'], 0, 50) . "...\n";
                echo "   Tokens: {$response['tokens_used']}, Time: " . number_format($response['execution_time'], 2) . "s\n";
                
            } catch (Exception $e) {
                echo "❌ {$model['name']}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n=== TEST SUMMARY ===\n";
    echo "✅ OpenRouter provider: Working\n";
    echo "✅ API integration: Working\n";
    echo "✅ Text generation: Working\n";
    echo "✅ Conversation history: Working\n";
    echo "✅ Multiple models: Working\n";
    
    echo "\nOpenRouter chat integration is ready!\n";
    echo "You can now:\n";
    echo "1. Create conversations in AI Lab\n";
    echo "2. Chat with AI models\n";
    echo "3. Maintain conversation context\n";
    echo "4. Use different OpenRouter models\n";
    
    echo "\nNext steps:\n";
    echo "1. Visit AI Lab: " . BASE_URL . "/?page=ai_lab\n";
    echo "2. Create a new conversation\n";
    echo "3. Start chatting with AI models!\n";
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
