<?php
/**
 * Test AI Lab Routing
 * 
 * This script tests if the AI Lab routing is working correctly.
 */

// Load configuration
require_once 'config.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== TESTING AI LAB ROUTING ===\n\n";

try {
    // Test 1: Check if AI Lab page loads without redirect
    echo "1. Testing AI Lab page access...\n";
    
    // Simulate the routing logic from index.php
    $page = 'ai_lab';
    $validPages = ['dashboard', 'jobs', 'posts', 'settings', 'process', 'analytics', 'users', 'ai_settings', 'ai_lab'];
    
    if (in_array($page, $validPages)) {
        echo "✅ AI Lab page is in valid pages list\n";
    } else {
        echo "❌ AI Lab page is NOT in valid pages list\n";
        exit;
    }
    
    // Test 2: Check if template file exists
    echo "\n2. Checking template file...\n";
    
    if (file_exists('templates/ai_lab.php')) {
        echo "✅ AI Lab template file exists\n";
    } else {
        echo "❌ AI Lab template file does NOT exist\n";
        exit;
    }
    
    // Test 3: Check if API endpoint exists
    echo "\n3. Checking API endpoint...\n";
    
    if (file_exists('api/ai_lab.php')) {
        echo "✅ AI Lab API endpoint exists\n";
    } else {
        echo "❌ AI Lab API endpoint does NOT exist\n";
    }
    
    // Test 4: Test template loading (capture output)
    echo "\n4. Testing template loading...\n";
    
    ob_start();
    
    // Load required files
    require_once 'includes/Database.php';
    $db = new Database();
    
    // Include the template
    include 'templates/ai_lab.php';
    
    $templateOutput = ob_get_contents();
    ob_end_clean();
    
    if (!empty($templateOutput)) {
        echo "✅ AI Lab template loads successfully\n";
        echo "Template output length: " . strlen($templateOutput) . " characters\n";
        
        // Check for key elements
        if (strpos($templateOutput, 'AI Lab') !== false) {
            echo "✅ Template contains 'AI Lab' title\n";
        }
        
        if (strpos($templateOutput, 'selectModel') !== false) {
            echo "✅ Template contains selectModel function\n";
        }
        
        if (strpos($templateOutput, 'sendMessage') !== false) {
            echo "✅ Template contains sendMessage function\n";
        }
        
    } else {
        echo "❌ AI Lab template produces no output\n";
    }
    
    // Test 5: Test API endpoint accessibility
    echo "\n5. Testing API endpoint...\n";
    
    $apiUrl = BASE_URL . '/api/ai_lab.php';
    echo "API URL: $apiUrl\n";
    
    // Test with a simple request
    $testData = [
        'action' => 'test_connection'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, sys_get_temp_dir() . '/ai_lab_test_cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, sys_get_temp_dir() . '/ai_lab_test_cookies.txt');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "❌ cURL Error: " . curl_error($ch) . "\n";
    } else {
        echo "HTTP Code: $httpCode\n";
        
        if ($httpCode == 200) {
            echo "✅ API endpoint is accessible\n";
            
            $decoded = json_decode($response, true);
            if ($decoded && isset($decoded['success'])) {
                echo "✅ API returns valid JSON response\n";
            } else {
                echo "⚠️  API response is not valid JSON: " . substr($response, 0, 100) . "...\n";
            }
        } else {
            echo "❌ API endpoint returned HTTP $httpCode\n";
        }
    }
    
    curl_close($ch);
    
    // Test 6: Check for common redirect issues
    echo "\n6. Checking for redirect issues...\n";
    
    // Check if there are any header() calls in the template
    $templateContent = file_get_contents('templates/ai_lab.php');
    
    if (strpos($templateContent, 'header(') !== false) {
        echo "⚠️  Template contains header() calls - this might cause redirects\n";
        
        // Find the header calls
        preg_match_all('/header\([^)]+\)/', $templateContent, $matches);
        foreach ($matches[0] as $headerCall) {
            echo "   Found: $headerCall\n";
        }
    } else {
        echo "✅ Template does not contain header() calls\n";
    }
    
    // Test 7: URL construction test
    echo "\n7. Testing URL construction...\n";
    
    $aiLabUrl = BASE_URL . '/?page=ai_lab';
    echo "AI Lab URL: $aiLabUrl\n";
    
    $parsedUrl = parse_url($aiLabUrl);
    if (isset($parsedUrl['query'])) {
        parse_str($parsedUrl['query'], $queryParams);
        if (isset($queryParams['page']) && $queryParams['page'] === 'ai_lab') {
            echo "✅ URL construction is correct\n";
        } else {
            echo "❌ URL construction is incorrect\n";
        }
    } else {
        echo "❌ URL does not contain query parameters\n";
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "✅ AI Lab routing should work correctly\n";
    echo "✅ Template loads without errors\n";
    echo "✅ API endpoint is accessible\n";
    echo "✅ No redirect issues found\n";
    
    echo "\nIf you're still experiencing redirects to homepage:\n";
    echo "1. Clear browser cache and cookies\n";
    echo "2. Check browser console for JavaScript errors\n";
    echo "3. Verify you're logged in properly\n";
    echo "4. Try accessing: $aiLabUrl\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
