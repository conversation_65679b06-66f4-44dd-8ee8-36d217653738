<?php
/**
 * AI Lab AJAX API - Debug Version
 * 
 * This script helps debug the AI Lab API issues.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

// Load configuration
require_once '../config.php';
require_once '../includes/Database.php';
require_once '../includes/AILabManager.php';

// Start session
session_start();

// Log the request
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'post_data' => $_POST,
    'session_user_id' => $_SESSION['user_id'] ?? 'NOT_SET',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'NOT_SET'
];

// Check for any unexpected output
$unexpectedOutput = ob_get_contents();
ob_end_clean();

// Set content type
header('Content-Type: application/json');

// Log unexpected output if any
if (!empty($unexpectedOutput)) {
    echo json_encode([
        'success' => false, 
        'error' => 'Unexpected output detected',
        'unexpected_output' => $unexpectedOutput,
        'log_data' => $logData
    ]);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false, 
        'error' => 'Method not allowed',
        'log_data' => $logData
    ]);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false, 
        'error' => 'Unauthorized',
        'log_data' => $logData
    ]);
    exit;
}

// Initialize database and lab manager
try {
    $db = new Database();
    $labManager = new AILabManager($db, $_SESSION['user_id']);
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'error' => 'Database connection failed: ' . $e->getMessage(),
        'log_data' => $logData
    ]);
    exit;
}

// Get action from POST data
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'send_message_direct':
            $providerId = (int)($_POST['provider_id'] ?? 0);
            $modelId = (int)($_POST['model_id'] ?? 0);
            $message = $_POST['message'] ?? '';
            $conversationHistory = json_decode($_POST['conversation_history'] ?? '[]', true);
            
            // Debug info
            $debugInfo = [
                'provider_id' => $providerId,
                'model_id' => $modelId,
                'message_length' => strlen($message),
                'history_count' => count($conversationHistory)
            ];
            
            if (!$providerId || !$modelId) {
                throw new Exception('Provider ID and Model ID are required');
            }
            
            if (empty($message)) {
                throw new Exception('Message cannot be empty');
            }
            
            // Load AI provider
            require_once '../includes/AIProvider.php';
            $provider = $db->getRow("SELECT slug FROM ai_providers WHERE id = ? AND is_active = 1", [$providerId]);
            if (!$provider) {
                throw new Exception('Provider not found or not active');
            }
            
            $debugInfo['provider_slug'] = $provider['slug'];
            
            try {
                $aiProvider = AIProvider::create($db, $provider['slug']);
                $debugInfo['ai_provider_created'] = true;
            } catch (Exception $e) {
                throw new Exception('Failed to create AI provider: ' . $e->getMessage());
            }
            
            // Generate response
            try {
                $response = $aiProvider->generateText($message, [
                    'model_id' => $modelId,
                    'conversation_history' => $conversationHistory
                ]);
                $debugInfo['ai_response_generated'] = true;
            } catch (Exception $e) {
                throw new Exception('Failed to generate AI response: ' . $e->getMessage());
            }
            
            echo json_encode([
                'success' => true,
                'response' => $response['text'],
                'tokens_used' => $response['tokens_used'] ?? 0,
                'execution_time' => $response['execution_time'] ?? 0,
                'model_used' => $response['model'] ?? 'Unknown',
                'debug_info' => $debugInfo,
                'log_data' => $logData
            ]);
            break;
            
        case 'test_connection':
            // Simple test to verify the API is working
            echo json_encode([
                'success' => true,
                'message' => 'AI Lab Debug API is working',
                'user_id' => $_SESSION['user_id'],
                'timestamp' => date('Y-m-d H:i:s'),
                'log_data' => $logData
            ]);
            break;
            
        default:
            throw new Exception('Invalid action: ' . $action);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'log_data' => $logData
    ]);
}
?>
