<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Content Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
</head>
<body>
    <!-- Settings Pre-Header -->
    <?php if (in_array($page, ['settings', 'ai_settings', 'users'])): ?>
    <div class="settings-preheader">
        <div class="container-fluid" style="max-width: 1400px; margin: 0 auto; padding: 0 1.5rem;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="fas fa-cog me-2 text-primary"></i>
                    <span class="fw-semibold text-dark">Configuration Center</span>
                </div>
                <nav class="settings-nav">
                    <ul class="nav nav-pills">
                        <li class="nav-item">
                            <a class="nav-link settings-nav-link <?php echo $page === 'settings' ? 'active' : ''; ?>"
                               href="<?php echo BASE_URL; ?>/?page=settings">
                                <i class="fas fa-cog me-1"></i>
                                General Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link settings-nav-link <?php echo $page === 'ai_settings' ? 'active' : ''; ?>"
                               href="<?php echo BASE_URL; ?>/?page=ai_settings">
                                <i class="fas fa-brain me-1"></i>
                                AI Settings
                            </a>
                        </li>
                        <?php
                        // Check if user has permission to manage users
                        try {
                            if (file_exists('includes/UserManager.php')) {
                                require_once 'includes/UserManager.php';
                                $userManager = new UserManager($db);
                                if (isset($_SESSION['user_id']) && $userManager->hasPermission($_SESSION['user_id'], 'manage_users')):
                                ?>
                                <li class="nav-item">
                                    <a class="nav-link settings-nav-link <?php echo $page === 'users' ? 'active' : ''; ?>"
                                       href="<?php echo BASE_URL; ?>/?page=users">
                                        <i class="fas fa-users me-1"></i>
                                        User Management
                                    </a>
                                </li>
                                <?php
                                endif;
                            }
                        } catch (Exception $e) {
                            // Ignore errors if UserManager is not available yet
                        }
                        ?>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Fixed Modern Navigation -->
    <nav class="navbar navbar-expand-lg modern-navbar">
        <div class="container-fluid" style="max-width: 1400px; margin: 0 auto; padding: 0 1.5rem;">
            <!-- Brand Section -->
            <a class="navbar-brand modern-brand" href="<?php echo BASE_URL; ?>">
                <div class="brand-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">AiCG</span>
                </div>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler modern-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="toggler-line"></span>
                <span class="toggler-line"></span>
                <span class="toggler-line"></span>
            </button>

            <!-- Navigation Content -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Navigation Links -->
                <ul class="navbar-nav me-auto modern-nav-list">
                    <li class="nav-item">
                        <a class="nav-link modern-nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>">
                            <i class="fas fa-home nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link modern-nav-link <?php echo $page === 'jobs' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=jobs">
                            <i class="fas fa-rocket nav-icon"></i>
                            <span class="nav-text">Jobs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link modern-nav-link <?php echo $page === 'posts' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=posts">
                            <i class="fas fa-newspaper nav-icon"></i>
                            <span class="nav-text">Posts</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link modern-nav-link <?php echo $page === 'analytics' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=analytics">
                            <i class="fas fa-chart-line nav-icon"></i>
                            <span class="nav-text">Analytics</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link modern-nav-link <?php echo $page === 'ai_lab' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_lab">
                            <i class="fas fa-flask nav-icon"></i>
                            <span class="nav-text">AI Lab</span>
                        </a>
                    </li>
                </ul>

                <!-- User Section -->
                <div class="d-flex align-items-center">
                    <?php if (isset($_SESSION['user_id']) && isset($currentUser)): ?>
                    <div class="dropdown">
                        <button class="btn modern-user-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info d-none d-md-block">
                                <span class="user-name"><?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?></span>
                                <span class="user-role">Administrator</span>
                            </div>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end modern-dropdown">
                            <li class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="dropdown-avatar me-3">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold"><?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?></div>
                                        <div class="text-muted small"><EMAIL></div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item modern-dropdown-item" href="<?php echo BASE_URL; ?>/?page=settings">
                                    <i class="fas fa-user-cog me-2"></i>
                                    Profile Settings
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item modern-dropdown-item" href="<?php echo BASE_URL; ?>/?page=ai_settings">
                                    <i class="fas fa-brain me-2"></i>
                                    AI Configuration
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item modern-dropdown-item text-danger" href="<?php echo BASE_URL; ?>/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Sign Out
                                </a>
                            </li>
                        </ul>
                    </div>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/login.php" class="btn modern-login-btn">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid px-4 py-4" style="max-width: 1400px; margin: 0 auto;">
        <?php if (isset($_SESSION) && isset($_SESSION['db_update_required']) && $_SESSION['db_update_required']): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Database update required!</strong> Your database needs to be updated to the latest version.
                    <div class="mt-2">
                        <a href="<?php echo BASE_URL; ?>/update_db.php" class="btn btn-sm btn-warning">Update Database Now</a>
                        <a href="<?php echo BASE_URL; ?>/?page=settings" class="btn btn-sm btn-outline-secondary ms-2">Go to Settings</a>
                    </div>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($pageTitle)): ?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0"><?php echo $pageTitle; ?></h1>
            <?php if (isset($pageActions)): ?>
            <div>
                <?php echo $pageActions; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
