<?php
/**
 * Update AI Tables
 *
 * This script creates or updates the database tables needed for AI processing functionality.
 */

// Load configuration
require_once 'config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load required files
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Include tool header
$pageTitle = 'Update AI Tables';
$currentPage = 'update_ai_tables';
include_once 'includes/tool_header.php';

echo '<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">AI Database Schema Update</h5>
    </div>
    <div class="card-body">
        <pre>';

echo "Checking and updating AI database schema...\n\n";

// Start transaction
$db->beginTransaction();

try {
    // Check if ai_providers table exists
    $aiProvidersExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    if (empty($aiProvidersExists)) {
        echo "Creating ai_providers table...\n";

        $db->query("
            CREATE TABLE ai_providers (
                id INT(11) NOT NULL AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL,
                description TEXT DEFAULT NULL,
                website VARCHAR(255) DEFAULT NULL,
                api_base_url VARCHAR(500) DEFAULT NULL,
                auth_type ENUM('bearer','api_key','custom') NOT NULL DEFAULT 'bearer',
                custom_headers JSON DEFAULT NULL,
                rate_limit_per_minute INT(11) DEFAULT NULL,
                supports_streaming TINYINT(1) NOT NULL DEFAULT 0,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY slug (slug)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Insert default provider (NOVITA)
        $providerId = $db->insert('ai_providers', [
            'name' => 'NOVITA AI',
            'slug' => 'novita',
            'description' => 'NOVITA AI provides advanced text and image generation models with state-of-the-art capabilities.',
            'website' => 'https://novita.ai',
            'is_active' => 1
        ]);

        echo "Added NOVITA AI provider with ID: $providerId\n";

        echo "ai_providers table created and default provider added.\n";
    } else {
        echo "ai_providers table already exists.\n";

        // Check if there are any providers
        $providersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers");
        if ($providersCount == 0) {
            echo "No providers found. Adding default NOVITA AI provider...\n";

            // Insert default provider (NOVITA)
            $providerId = $db->insert('ai_providers', [
                'name' => 'NOVITA AI',
                'slug' => 'novita',
                'description' => 'NOVITA AI provides advanced text and image generation models with state-of-the-art capabilities.',
                'website' => 'https://novita.ai',
                'is_active' => 1
            ]);

            echo "Added NOVITA AI provider with ID: $providerId\n";
        } else {
            echo "Found $providersCount providers in the database.\n";
        }
    }

    // Check if ai_api_keys table exists
    $aiApiKeysExists = $db->query("SHOW TABLES LIKE 'ai_api_keys'");
    if (empty($aiApiKeysExists)) {
        echo "Creating ai_api_keys table...\n";

        $db->query("
            CREATE TABLE ai_api_keys (
                id INT(11) NOT NULL AUTO_INCREMENT,
                provider_id INT(11) NOT NULL,
                name VARCHAR(255) NOT NULL,
                api_key VARCHAR(255) NOT NULL,
                is_default TINYINT(1) NOT NULL DEFAULT 0,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                usage_count INT(11) NOT NULL DEFAULT 0,
                last_used DATETIME DEFAULT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY provider_id (provider_id),
                CONSTRAINT fk_api_keys_provider FOREIGN KEY (provider_id) REFERENCES ai_providers (id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        echo "ai_api_keys table created.\n";

        // Add a default API key for NOVITA AI
        $providerId = $db->getValue("SELECT id FROM ai_providers WHERE slug = 'novita'");
        if ($providerId) {
            try {
                // Insert default API key
                $apiKeyId = $db->insert('ai_api_keys', [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA AI Default Key',
                    'api_key' => 'sk-novita-default-key-' . time(),
                    'is_default' => 1,
                    'is_active' => 1,
                    'usage_count' => 0
                ]);

                echo "Added default API key for NOVITA AI with ID: $apiKeyId\n";

                // Verify the key was added
                $verifyKey = $db->getRow("SELECT * FROM ai_api_keys WHERE id = ?", [$apiKeyId]);
                if ($verifyKey) {
                    echo "Successfully verified the API key was added.\n";
                } else {
                    echo "Warning: Could not verify the API key was added.\n";
                }
            } catch (Exception $e) {
                echo "Error adding default API key: " . $e->getMessage() . "\n";
            }
        }
    } else {
        echo "ai_api_keys table already exists.\n";

        // Check if the usage_count column exists
        $columns = $db->query("SHOW COLUMNS FROM ai_api_keys LIKE 'usage_count'");
        if (empty($columns)) {
            echo "Adding usage_count column to ai_api_keys table...\n";
            $db->query("ALTER TABLE ai_api_keys ADD COLUMN usage_count INT(11) NOT NULL DEFAULT 0 AFTER is_active");
            echo "usage_count column added.\n";
        }

        // Check if there are any API keys
        $apiKeysCount = $db->getValue("SELECT COUNT(*) FROM ai_api_keys");
        if ($apiKeysCount == 0) {
            echo "No API keys found. Adding default API key...\n";

            // Add a default API key for NOVITA AI
            $providerId = $db->getValue("SELECT id FROM ai_providers WHERE slug = 'novita'");
            if ($providerId) {
                try {
                    // Insert default API key
                    $apiKeyId = $db->insert('ai_api_keys', [
                        'provider_id' => $providerId,
                        'name' => 'NOVITA AI Default Key',
                        'api_key' => 'sk-novita-default-key-' . time(),
                        'is_default' => 1,
                        'is_active' => 1,
                        'usage_count' => 0
                    ]);

                    echo "Added default API key for NOVITA AI with ID: $apiKeyId\n";

                    // Verify the key was added
                    $verifyKey = $db->getRow("SELECT * FROM ai_api_keys WHERE id = ?", [$apiKeyId]);
                    if ($verifyKey) {
                        echo "Successfully verified the API key was added.\n";
                    } else {
                        echo "Warning: Could not verify the API key was added.\n";
                    }
                } catch (Exception $e) {
                    echo "Error adding default API key: " . $e->getMessage() . "\n";
                }
            }
        } else {
            echo "Found $apiKeysCount API keys in the database.\n";
        }
    }

    // Check if ai_models table exists
    $aiModelsExists = $db->query("SHOW TABLES LIKE 'ai_models'");
    if (empty($aiModelsExists)) {
        echo "Creating ai_models table...\n";

        $db->query("
            CREATE TABLE ai_models (
                id INT(11) NOT NULL AUTO_INCREMENT,
                provider_id INT(11) NOT NULL,
                name VARCHAR(255) NOT NULL,
                model_id VARCHAR(255) NOT NULL,
                type ENUM('text', 'image', 'both') NOT NULL DEFAULT 'text',
                capabilities JSON DEFAULT NULL,
                system_prompt TEXT DEFAULT NULL,
                max_tokens INT(11) DEFAULT NULL,
                temperature FLOAT DEFAULT 0.7,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY provider_id (provider_id),
                KEY type (type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Insert some default models for NOVITA
        $providerId = $db->getValue("SELECT id FROM ai_providers WHERE slug = 'novita'");
        if ($providerId) {
            $defaultModels = [
                [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA GPT-4',
                    'model_id' => 'gpt-4',
                    'type' => 'text',
                    'capabilities' => json_encode(['translate', 'rewrite', 'summarize', 'optimize']),
                    'system_prompt' => 'You are a helpful assistant that can translate, rewrite, and optimize content.',
                    'max_tokens' => 4096,
                    'temperature' => 0.7,
                    'is_active' => 1
                ],
                [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA GPT-3.5 Turbo',
                    'model_id' => 'gpt-3.5-turbo',
                    'type' => 'text',
                    'capabilities' => json_encode(['translate', 'rewrite', 'summarize']),
                    'system_prompt' => 'You are a helpful assistant that can translate, rewrite, and optimize content.',
                    'max_tokens' => 4096,
                    'temperature' => 0.7,
                    'is_active' => 1
                ],
                [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA Claude 3 Opus',
                    'model_id' => 'claude-3-opus',
                    'type' => 'text',
                    'capabilities' => json_encode(['translate', 'rewrite', 'summarize', 'optimize']),
                    'system_prompt' => 'You are a helpful assistant that can translate, rewrite, and optimize content.',
                    'max_tokens' => 4096,
                    'temperature' => 0.7,
                    'is_active' => 1
                ],
                [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA DALL-E 3',
                    'model_id' => 'dall-e-3',
                    'type' => 'image',
                    'capabilities' => json_encode(['generate']),
                    'system_prompt' => null,
                    'max_tokens' => null,
                    'temperature' => null,
                    'is_active' => 1
                ],
                [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA Stable Diffusion XL',
                    'model_id' => 'stable-diffusion-xl',
                    'type' => 'image',
                    'capabilities' => json_encode(['generate']),
                    'system_prompt' => null,
                    'max_tokens' => null,
                    'temperature' => null,
                    'is_active' => 1
                ],
                [
                    'provider_id' => $providerId,
                    'name' => 'NOVITA Midjourney',
                    'model_id' => 'midjourney',
                    'type' => 'image',
                    'capabilities' => json_encode(['generate']),
                    'system_prompt' => null,
                    'max_tokens' => null,
                    'temperature' => null,
                    'is_active' => 1
                ]
            ];

            foreach ($defaultModels as $model) {
                $db->insert('ai_models', $model);
            }
        }

        echo "ai_models table created and default models added.\n";
    } else {
        echo "ai_models table already exists.\n";
    }

    // Check if ai_workflows table exists
    $aiWorkflowsExists = $db->query("SHOW TABLES LIKE 'ai_workflows'");
    if (empty($aiWorkflowsExists)) {
        echo "Creating ai_workflows table...\n";

        $db->query("
            CREATE TABLE ai_workflows (
                id INT(11) NOT NULL AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                description TEXT DEFAULT NULL,
                is_default TINYINT(1) NOT NULL DEFAULT 0,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Insert default workflow
        $workflowId = $db->insert('ai_workflows', [
            'name' => 'Default Workflow',
            'description' => 'Default workflow for AI processing',
            'is_default' => 1,
            'is_active' => 1
        ]);

        echo "ai_workflows table created and default workflow added.\n";
    } else {
        echo "ai_workflows table already exists.\n";
    }

    // Check if ai_workflow_steps table exists
    $aiWorkflowStepsExists = $db->query("SHOW TABLES LIKE 'ai_workflow_steps'");
    if (empty($aiWorkflowStepsExists)) {
        echo "Creating ai_workflow_steps table...\n";

        $db->query("
            CREATE TABLE ai_workflow_steps (
                id INT(11) NOT NULL AUTO_INCREMENT,
                workflow_id INT(11) NOT NULL,
                model_id INT(11) NOT NULL,
                step_type ENUM('title', 'content', 'image_prompt', 'image_generation', 'category', 'tag', 'seo') NOT NULL,
                task_type ENUM('translate', 'rewrite', 'translate_rewrite', 'generate', 'optimize') NOT NULL,
                prompt_template TEXT DEFAULT NULL,
                order_index INT(11) NOT NULL DEFAULT 0,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY workflow_id (workflow_id),
                KEY model_id (model_id),
                KEY step_type (step_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        echo "ai_workflow_steps table created.\n";

        // Add default workflow steps if we have models and workflow
        $workflowId = $db->getValue("SELECT id FROM ai_workflows WHERE is_default = 1");
        $textModelId = $db->getValue("SELECT id FROM ai_models WHERE type = 'text' LIMIT 1");
        $imageModelId = $db->getValue("SELECT id FROM ai_models WHERE type = 'image' LIMIT 1");

        if ($workflowId && $textModelId && $imageModelId) {
            $defaultSteps = [
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'title',
                    'task_type' => 'translate',
                    'prompt_template' => 'Translate the following title to {{language}} while preserving its meaning and SEO value: {{title}}',
                    'order_index' => 1
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'title',
                    'task_type' => 'rewrite',
                    'prompt_template' => 'Rewrite the following title to make it more engaging and SEO-friendly while preserving its core meaning: {{title}}',
                    'order_index' => 2
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'title',
                    'task_type' => 'translate_rewrite',
                    'prompt_template' => 'Translate and rewrite the following title to {{language}}, making it more engaging and SEO-friendly while preserving its core meaning: {{title}}',
                    'order_index' => 3
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'content',
                    'task_type' => 'translate',
                    'prompt_template' => 'Translate the following content to {{language}} while preserving its formatting, meaning, and SEO value: {{content}}',
                    'order_index' => 4
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'content',
                    'task_type' => 'rewrite',
                    'prompt_template' => 'Rewrite the following content to make it more engaging and SEO-friendly while preserving its core meaning and HTML formatting: {{content}}',
                    'order_index' => 5
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'content',
                    'task_type' => 'translate_rewrite',
                    'prompt_template' => 'Translate and rewrite the following content to {{language}}, making it more engaging and SEO-friendly while preserving its core meaning and HTML formatting: {{content}}',
                    'order_index' => 6
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'image_prompt',
                    'task_type' => 'generate',
                    'prompt_template' => 'Create a detailed image generation prompt for a feature image that represents the following article. The prompt should be descriptive and creative: {{title}} {{excerpt}}',
                    'order_index' => 7
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $imageModelId,
                    'step_type' => 'image_generation',
                    'task_type' => 'generate',
                    'prompt_template' => '{{image_prompt}}',
                    'order_index' => 8
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'category',
                    'task_type' => 'generate',
                    'prompt_template' => 'Based on the following article, suggest 3-5 relevant categories. Return only the category names as a comma-separated list: {{title}} {{excerpt}}',
                    'order_index' => 9
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'tag',
                    'task_type' => 'generate',
                    'prompt_template' => 'Based on the following article, suggest 5-10 relevant tags. Return only the tag names as a comma-separated list: {{title}} {{excerpt}}',
                    'order_index' => 10
                ],
                [
                    'workflow_id' => $workflowId,
                    'model_id' => $textModelId,
                    'step_type' => 'seo',
                    'task_type' => 'optimize',
                    'prompt_template' => 'Generate SEO metadata for the following content. Include meta description (max 160 characters), keywords (5-7 comma-separated), and a slug: {{title}} {{excerpt}}',
                    'order_index' => 11
                ]
            ];

            foreach ($defaultSteps as $step) {
                $db->insert('ai_workflow_steps', $step);
            }

            echo "Default workflow steps added.\n";
        }
    } else {
        echo "ai_workflow_steps table already exists.\n";
    }

    // Check if ai_processed_posts table exists
    $aiProcessedPostsExists = $db->query("SHOW TABLES LIKE 'ai_processed_posts'");
    if (empty($aiProcessedPostsExists)) {
        echo "Creating ai_processed_posts table...\n";

        $db->query("
            CREATE TABLE ai_processed_posts (
                id INT(11) NOT NULL AUTO_INCREMENT,
                post_id INT(11) NOT NULL,
                workflow_id INT(11) NOT NULL,
                status ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending',
                language VARCHAR(10) DEFAULT 'en',
                is_rtl TINYINT(1) NOT NULL DEFAULT 0,
                processed_title TEXT DEFAULT NULL,
                processed_content LONGTEXT DEFAULT NULL,
                processed_excerpt TEXT DEFAULT NULL,
                image_prompt TEXT DEFAULT NULL,
                processed_featured_image VARCHAR(255) DEFAULT NULL,
                processed_categories TEXT DEFAULT NULL,
                processed_tags TEXT DEFAULT NULL,
                processed_seo_metadata TEXT DEFAULT NULL,
                error_message TEXT DEFAULT NULL,
                started_at DATETIME DEFAULT NULL,
                completed_at DATETIME DEFAULT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY post_id (post_id),
                KEY workflow_id (workflow_id),
                KEY status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        echo "ai_processed_posts table created.\n";
    } else {
        echo "ai_processed_posts table already exists.\n";
    }

    // Check if ai_processing_logs table exists
    $aiProcessingLogsExists = $db->query("SHOW TABLES LIKE 'ai_processing_logs'");
    if (empty($aiProcessingLogsExists)) {
        echo "Creating ai_processing_logs table...\n";

        $db->query("
            CREATE TABLE ai_processing_logs (
                id INT(11) NOT NULL AUTO_INCREMENT,
                processed_post_id INT(11) NOT NULL,
                step_type ENUM('title', 'content', 'image_prompt', 'image_generation', 'category', 'tag', 'seo') NOT NULL,
                task_type ENUM('translate', 'rewrite', 'translate_rewrite', 'generate', 'optimize') NOT NULL,
                model_id INT(11) NOT NULL,
                api_key_id INT(11) NOT NULL,
                prompt TEXT DEFAULT NULL,
                response TEXT DEFAULT NULL,
                tokens_used INT(11) DEFAULT NULL,
                execution_time FLOAT DEFAULT NULL,
                status ENUM('success', 'failed') NOT NULL DEFAULT 'success',
                error_message TEXT DEFAULT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY processed_post_id (processed_post_id),
                KEY model_id (model_id),
                KEY api_key_id (api_key_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        echo "ai_processing_logs table created.\n";
    } else {
        echo "ai_processing_logs table already exists.\n";
    }

    // Check if ai_lab_conversations table exists
    $aiLabConversationsExists = $db->query("SHOW TABLES LIKE 'ai_lab_conversations'");
    if (empty($aiLabConversationsExists)) {
        echo "Creating ai_lab_conversations table...\n";

        $db->query("
            CREATE TABLE ai_lab_conversations (
                id INT(11) NOT NULL AUTO_INCREMENT,
                user_id INT(11) NOT NULL,
                title VARCHAR(255) DEFAULT NULL,
                provider_id INT(11) NOT NULL,
                model_id INT(11) NOT NULL,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY user_id (user_id),
                KEY provider_id (provider_id),
                KEY model_id (model_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        echo "ai_lab_conversations table created.\n";
    } else {
        echo "ai_lab_conversations table already exists.\n";
    }

    // Check if ai_lab_messages table exists
    $aiLabMessagesExists = $db->query("SHOW TABLES LIKE 'ai_lab_messages'");
    if (empty($aiLabMessagesExists)) {
        echo "Creating ai_lab_messages table...\n";

        $db->query("
            CREATE TABLE ai_lab_messages (
                id INT(11) NOT NULL AUTO_INCREMENT,
                conversation_id INT(11) NOT NULL,
                role ENUM('user','assistant','system') NOT NULL,
                content LONGTEXT NOT NULL,
                tokens_used INT(11) DEFAULT NULL,
                execution_time FLOAT DEFAULT NULL,
                model_used VARCHAR(255) DEFAULT NULL,
                error_message TEXT DEFAULT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY conversation_id (conversation_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        echo "ai_lab_messages table created.\n";
    } else {
        echo "ai_lab_messages table already exists.\n";
    }

    // Update existing ai_providers table to add new columns if they don't exist
    echo "Checking for new columns in ai_providers table...\n";

    $providerColumns = $db->query("SHOW COLUMNS FROM ai_providers");
    $existingColumns = array_column($providerColumns, 'Field');

    if (!in_array('api_base_url', $existingColumns)) {
        echo "Adding api_base_url column to ai_providers...\n";
        $db->query("ALTER TABLE ai_providers ADD COLUMN api_base_url VARCHAR(500) DEFAULT NULL AFTER website");
    }

    if (!in_array('auth_type', $existingColumns)) {
        echo "Adding auth_type column to ai_providers...\n";
        $db->query("ALTER TABLE ai_providers ADD COLUMN auth_type ENUM('bearer','api_key','custom') NOT NULL DEFAULT 'bearer' AFTER api_base_url");
    }

    if (!in_array('custom_headers', $existingColumns)) {
        echo "Adding custom_headers column to ai_providers...\n";
        $db->query("ALTER TABLE ai_providers ADD COLUMN custom_headers JSON DEFAULT NULL AFTER auth_type");
    }

    if (!in_array('rate_limit_per_minute', $existingColumns)) {
        echo "Adding rate_limit_per_minute column to ai_providers...\n";
        $db->query("ALTER TABLE ai_providers ADD COLUMN rate_limit_per_minute INT(11) DEFAULT NULL AFTER custom_headers");
    }

    if (!in_array('supports_streaming', $existingColumns)) {
        echo "Adding supports_streaming column to ai_providers...\n";
        $db->query("ALTER TABLE ai_providers ADD COLUMN supports_streaming TINYINT(1) NOT NULL DEFAULT 0 AFTER rate_limit_per_minute");
    }

    // Commit transaction
    $db->commit();

    echo "\nAI database schema update completed successfully!\n";
} catch (Exception $e) {
    // Rollback transaction
    $db->rollback();

    echo "\nError updating AI database schema: " . $e->getMessage() . "\n";
}

echo '</pre>
    </div>
    <div class="card-footer bg-white">
        <a href="' . BASE_URL . '/?page=ai_settings" class="btn btn-primary">
            <i class="fas fa-cog me-1"></i> Go to AI Settings
        </a>
    </div>
</div>';

// Include tool footer
include_once 'includes/tool_footer.php';
?>
