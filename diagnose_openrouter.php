<?php
/**
 * Diagnose OpenRouter Setup
 * 
 * This script checks the complete OpenRouter setup and tests the connection.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== OPENROUTER DIAGNOSIS ===\n\n";

try {
    $db = new Database();
    $pdo = get_db_connection();
    
    // Step 1: Check OpenRouter provider
    echo "1. Checking OpenRouter provider...\n";
    
    $provider = $db->getRow("SELECT * FROM ai_providers WHERE slug = 'openrouter'");
    if (!$provider) {
        echo "❌ OpenRouter provider not found in database\n";
        echo "Creating OpenRouter provider...\n";
        
        $providerId = $db->insert('ai_providers', [
            'name' => 'OpenRouter',
            'slug' => 'openrouter',
            'description' => 'Access to multiple AI models through a unified API',
            'website' => 'https://openrouter.ai',
            'api_base_url' => 'https://openrouter.ai/api/v1',
            'auth_type' => 'bearer',
            'is_active' => 1
        ]);
        
        $provider = $db->getRow("SELECT * FROM ai_providers WHERE id = ?", [$providerId]);
        echo "✅ Created OpenRouter provider with ID: $providerId\n";
    } else {
        echo "✅ OpenRouter provider found:\n";
        echo "   ID: {$provider['id']}\n";
        echo "   Name: {$provider['name']}\n";
        echo "   Active: " . ($provider['is_active'] ? 'Yes' : 'No') . "\n";
        echo "   API Base URL: {$provider['api_base_url']}\n";
    }
    
    // Step 2: Check API key
    echo "\n2. Checking OpenRouter API key...\n";
    
    $apiKey = $db->getRow("SELECT * FROM ai_api_keys WHERE provider_id = ?", [$provider['id']]);
    if (!$apiKey) {
        echo "❌ No API key found for OpenRouter\n";
        echo "Please add your OpenRouter API key in AI Settings > API Keys\n";
        exit;
    } else {
        echo "✅ API key found:\n";
        echo "   Key ID: {$apiKey['id']}\n";
        echo "   Key Name: {$apiKey['key_name']}\n";
        echo "   Active: " . ($apiKey['is_active'] ? 'Yes' : 'No') . "\n";
        echo "   Key (masked): " . substr($apiKey['api_key'], 0, 8) . "..." . substr($apiKey['api_key'], -4) . "\n";
        
        if (!$apiKey['is_active']) {
            echo "⚠️  API key is not active. Activating...\n";
            $db->update('ai_api_keys', ['is_active' => 1], 'id = ?', [$apiKey['id']]);
            echo "✅ API key activated\n";
        }
    }
    
    // Step 3: Check OpenRouter models
    echo "\n3. Checking OpenRouter models...\n";
    
    $models = $db->query("SELECT * FROM ai_models WHERE provider_id = ?", [$provider['id']]);
    if (empty($models)) {
        echo "❌ No OpenRouter models found\n";
        echo "Creating test models...\n";
        
        $testModels = [
            [
                'name' => 'GPT-4 Turbo',
                'model_id' => 'openai/gpt-4-turbo',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ],
            [
                'name' => 'Claude 3 Sonnet',
                'model_id' => 'anthropic/claude-3-sonnet',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ]
        ];
        
        foreach ($testModels as $modelData) {
            $modelData['provider_id'] = $provider['id'];
            $modelId = $db->insert('ai_models', $modelData);
            echo "✅ Created model: {$modelData['name']} (ID: $modelId)\n";
        }
        
        $models = $db->query("SELECT * FROM ai_models WHERE provider_id = ?", [$provider['id']]);
    } else {
        echo "✅ Found " . count($models) . " OpenRouter models:\n";
        foreach ($models as $model) {
            $status = $model['is_active'] ? 'ACTIVE' : 'INACTIVE';
            echo "   • {$model['name']} ({$model['model_id']}) - $status\n";
        }
    }
    
    // Step 4: Test OpenRouter API connection
    echo "\n4. Testing OpenRouter API connection...\n";
    
    $testModel = $models[0]; // Use first model
    echo "Using model: {$testModel['name']} ({$testModel['model_id']})\n";
    
    // Prepare API request
    $apiUrl = $provider['api_base_url'] . '/chat/completions';
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey['api_key'],
        'HTTP-Referer: ' . BASE_URL,
        'X-Title: AI Content Grabber'
    ];
    
    $requestData = [
        'model' => $testModel['model_id'],
        'messages' => [
            ['role' => 'user', 'content' => 'Hello! Please respond with just "API connection successful"']
        ],
        'max_tokens' => 50,
        'temperature' => 0.1
    ];
    
    echo "API URL: $apiUrl\n";
    echo "Request data: " . json_encode($requestData, JSON_PRETTY_PRINT) . "\n";
    
    // Make API request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($curlError) {
        echo "❌ cURL Error: $curlError\n";
        exit;
    }
    
    if ($httpCode !== 200) {
        echo "❌ API request failed with HTTP $httpCode\n";
        echo "Response: $response\n";
        exit;
    }
    
    $responseData = json_decode($response, true);
    if (!$responseData) {
        echo "❌ Invalid JSON response\n";
        echo "Raw response: $response\n";
        exit;
    }
    
    if (isset($responseData['error'])) {
        echo "❌ API Error: {$responseData['error']['message']}\n";
        echo "Error type: {$responseData['error']['type']}\n";
        exit;
    }
    
    if (isset($responseData['choices'][0]['message']['content'])) {
        $aiResponse = $responseData['choices'][0]['message']['content'];
        echo "✅ API connection successful!\n";
        echo "AI Response: $aiResponse\n";
        echo "Tokens used: " . ($responseData['usage']['total_tokens'] ?? 'Unknown') . "\n";
    } else {
        echo "❌ Unexpected response format\n";
        echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
        exit;
    }
    
    // Step 5: Test AI Provider class
    echo "\n5. Testing AI Provider class...\n";
    
    try {
        require_once 'includes/AIProvider.php';
        $aiProvider = AIProvider::create($db, 'openrouter');
        echo "✅ AI Provider created successfully\n";
        
        $testResponse = $aiProvider->generateText('Say hello', [
            'model_id' => $testModel['id'],
            'max_tokens' => 50
        ]);
        
        echo "✅ AI Provider test successful!\n";
        echo "Response: " . substr($testResponse['text'], 0, 100) . "...\n";
        echo "Tokens: {$testResponse['tokens_used']}\n";
        echo "Time: " . number_format($testResponse['execution_time'], 2) . "s\n";
        
    } catch (Exception $e) {
        echo "❌ AI Provider test failed: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        exit;
    }
    
    // Step 6: Test debug API endpoint
    echo "\n6. Testing debug API endpoint...\n";
    
    $debugApiUrl = BASE_URL . '/api/ai_lab_debug.php';
    $testData = [
        'action' => 'send_message_direct',
        'provider_id' => $provider['id'],
        'model_id' => $testModel['id'],
        'message' => 'Hello from diagnosis script',
        'conversation_history' => json_encode([])
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $debugApiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, sys_get_temp_dir() . '/diagnosis_cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, sys_get_temp_dir() . '/diagnosis_cookies.txt');
    
    $debugResponse = curl_exec($ch);
    $debugHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Debug API HTTP Code: $debugHttpCode\n";
    echo "Debug API Response: " . substr($debugResponse, 0, 500) . "...\n";
    
    $debugData = json_decode($debugResponse, true);
    if ($debugData && $debugData['success']) {
        echo "✅ Debug API test successful!\n";
        echo "AI Response: " . substr($debugData['response'], 0, 100) . "...\n";
    } else {
        echo "❌ Debug API test failed\n";
        if ($debugData && isset($debugData['error'])) {
            echo "Error: {$debugData['error']}\n";
        }
    }
    
    echo "\n=== DIAGNOSIS COMPLETE ===\n";
    echo "✅ OpenRouter setup is working correctly!\n";
    echo "✅ API key is valid and active\n";
    echo "✅ Models are available\n";
    echo "✅ AI Provider class works\n";
    echo "✅ Debug API endpoint works\n";
    
    echo "\nYou should now be able to use the AI Lab successfully!\n";
    echo "Visit: " . BASE_URL . "/?page=ai_lab\n";
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== END DIAGNOSIS ===\n";
?>
