<?php
/**
 * Debug Models
 * 
 * This script helps debug why models aren't appearing in the list.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Include tool header
$pageTitle = 'Debug Models';
$currentPage = 'debug_models';
include_once 'includes/tool_header.php';

echo '<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Models Debug Information</h5>
    </div>
    <div class="card-body">
        <pre>';

echo "Debugging AI Models...\n\n";

try {
    // Check if tables exist
    echo "=== TABLE EXISTENCE CHECK ===\n";
    $providersTableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");
    
    echo "ai_providers table exists: " . (!empty($providersTableExists) ? "YES" : "NO") . "\n";
    echo "ai_models table exists: " . (!empty($modelsTableExists) ? "YES" : "NO") . "\n\n";
    
    if (empty($providersTableExists) || empty($modelsTableExists)) {
        echo "❌ Required tables are missing. Please run the database update script.\n";
        echo '<a href="' . BASE_URL . '/update_ai_tables.php" class="btn btn-primary">Update AI Tables</a>';
        exit;
    }
    
    // Check providers
    echo "=== PROVIDERS ===\n";
    $providers = $db->query("SELECT id, name, slug, is_active FROM ai_providers ORDER BY name");
    if (empty($providers)) {
        echo "❌ No providers found in database.\n";
        echo '<a href="' . BASE_URL . '/setup_default_providers.php" class="btn btn-success">Setup Default Providers</a>';
    } else {
        echo "Found " . count($providers) . " providers:\n";
        foreach ($providers as $provider) {
            $status = $provider['is_active'] ? "ACTIVE" : "INACTIVE";
            echo "  • ID: {$provider['id']}, Name: {$provider['name']}, Slug: {$provider['slug']}, Status: $status\n";
        }
    }
    echo "\n";
    
    // Check models (raw data)
    echo "=== MODELS (Raw Data) ===\n";
    $modelsRaw = $db->query("SELECT * FROM ai_models ORDER BY id");
    if (empty($modelsRaw)) {
        echo "❌ No models found in database.\n";
    } else {
        echo "Found " . count($modelsRaw) . " models:\n";
        foreach ($modelsRaw as $model) {
            $status = $model['is_active'] ? "ACTIVE" : "INACTIVE";
            echo "  • ID: {$model['id']}, Provider ID: {$model['provider_id']}, Name: {$model['name']}, Model ID: {$model['model_id']}, Status: $status\n";
        }
    }
    echo "\n";
    
    // Check models with JOIN (same as in template)
    echo "=== MODELS (With JOIN - Template Query) ===\n";
    $modelsWithJoin = $db->query("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        ORDER BY p.name, m.name
    ");
    
    if (empty($modelsWithJoin)) {
        echo "❌ No models found with JOIN query.\n";
        
        // Check for orphaned models
        echo "\n=== CHECKING FOR ORPHANED MODELS ===\n";
        $orphanedModels = $db->query("
            SELECT m.* 
            FROM ai_models m 
            LEFT JOIN ai_providers p ON m.provider_id = p.id 
            WHERE p.id IS NULL
        ");
        
        if (!empty($orphanedModels)) {
            echo "⚠️  Found " . count($orphanedModels) . " orphaned models (models with invalid provider_id):\n";
            foreach ($orphanedModels as $model) {
                echo "  • Model ID: {$model['id']}, Name: {$model['name']}, Invalid Provider ID: {$model['provider_id']}\n";
            }
        } else {
            echo "✅ No orphaned models found.\n";
        }
        
        // Check for inactive providers
        echo "\n=== CHECKING FOR MODELS WITH INACTIVE PROVIDERS ===\n";
        $modelsWithInactiveProviders = $db->query("
            SELECT m.*, p.name as provider_name, p.is_active as provider_active
            FROM ai_models m
            JOIN ai_providers p ON m.provider_id = p.id
            WHERE p.is_active = 0
        ");
        
        if (!empty($modelsWithInactiveProviders)) {
            echo "⚠️  Found " . count($modelsWithInactiveProviders) . " models with inactive providers:\n";
            foreach ($modelsWithInactiveProviders as $model) {
                echo "  • Model: {$model['name']}, Provider: {$model['provider_name']} (INACTIVE)\n";
            }
        } else {
            echo "✅ No models with inactive providers found.\n";
        }
        
    } else {
        echo "✅ Found " . count($modelsWithJoin) . " models with JOIN query:\n";
        foreach ($modelsWithJoin as $model) {
            $status = $model['is_active'] ? "ACTIVE" : "INACTIVE";
            echo "  • {$model['provider_name']} / {$model['name']} ({$model['model_id']}) - $status\n";
        }
    }
    
    // Check recent insertions
    echo "\n=== RECENT MODEL INSERTIONS ===\n";
    $recentModels = $db->query("
        SELECT m.*, p.name as provider_name 
        FROM ai_models m 
        LEFT JOIN ai_providers p ON m.provider_id = p.id 
        ORDER BY m.created_at DESC 
        LIMIT 5
    ");
    
    if (!empty($recentModels)) {
        echo "Last 5 models added:\n";
        foreach ($recentModels as $model) {
            $providerName = $model['provider_name'] ?? 'UNKNOWN PROVIDER';
            echo "  • {$model['created_at']}: {$providerName} / {$model['name']} (Provider ID: {$model['provider_id']})\n";
        }
    } else {
        echo "No recent models found.\n";
    }
    
    // Suggest fixes
    echo "\n=== SUGGESTED FIXES ===\n";
    
    if (empty($providers)) {
        echo "1. ❌ No providers found - Run setup_default_providers.php\n";
    }
    
    if (!empty($modelsRaw) && empty($modelsWithJoin)) {
        echo "2. ❌ Models exist but JOIN fails - Check provider relationships\n";
    }
    
    if (empty($modelsRaw)) {
        echo "3. ❌ No models in database - Add models through AI Settings\n";
    }
    
    // Check if the most recent model has valid provider
    if (!empty($recentModels)) {
        $latestModel = $recentModels[0];
        if (empty($latestModel['provider_name'])) {
            echo "4. ❌ Latest model has invalid provider_id ({$latestModel['provider_id']}) - Check provider exists\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error during debug: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo '</pre>
    </div>
    <div class="card-footer bg-white">
        <div class="row">
            <div class="col-md-3">
                <a href="' . BASE_URL . '/update_ai_tables.php" class="btn btn-primary">
                    <i class="fas fa-database me-1"></i> Update Tables
                </a>
            </div>
            <div class="col-md-3">
                <a href="' . BASE_URL . '/setup_default_providers.php" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i> Setup Providers
                </a>
            </div>
            <div class="col-md-3">
                <a href="' . BASE_URL . '/?page=ai_settings&section=models" class="btn btn-outline-primary">
                    <i class="fas fa-brain me-1"></i> AI Models
                </a>
            </div>
            <div class="col-md-3">
                <a href="' . BASE_URL . '/?page=ai_settings&section=providers" class="btn btn-outline-secondary">
                    <i class="fas fa-robot me-1"></i> AI Providers
                </a>
            </div>
        </div>
    </div>
</div>';

// Include tool footer
include_once 'includes/tool_footer.php';
?>
