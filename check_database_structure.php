<?php
/**
 * Check Database Structure
 * 
 * This script checks the database structure for AI-related tables.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

header('Content-Type: text/plain');

echo "=== DATABASE STRUCTURE CHECK ===\n\n";

try {
    $pdo = get_db_connection();
    
    // Check AI-related tables
    $tables = ['ai_providers', 'ai_models', 'ai_api_keys', 'ai_lab_conversations', 'ai_lab_messages'];
    
    foreach ($tables as $table) {
        echo "Checking table: $table\n";
        
        // Check if table exists
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetchColumn();
        
        if (!$exists) {
            echo "❌ Table $table does not exist\n\n";
            continue;
        }
        
        echo "✅ Table $table exists\n";
        
        // Show table structure
        $stmt = $pdo->prepare("DESCRIBE $table");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Columns:\n";
        foreach ($columns as $column) {
            echo "   • {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Key']}\n";
        }
        
        // Show row count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM $table");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        echo "Row count: $count\n";
        
        // Show sample data for key tables
        if (in_array($table, ['ai_providers', 'ai_models', 'ai_api_keys']) && $count > 0) {
            $stmt = $pdo->prepare("SELECT * FROM $table LIMIT 3");
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "Sample data:\n";
            foreach ($rows as $row) {
                echo "   Row: " . json_encode($row) . "\n";
            }
        }
        
        echo "\n";
    }
    
    // Check specific OpenRouter data
    echo "=== OPENROUTER SPECIFIC CHECK ===\n";
    
    // Check OpenRouter provider
    $stmt = $pdo->prepare("SELECT * FROM ai_providers WHERE slug = 'openrouter'");
    $stmt->execute();
    $openrouterProvider = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($openrouterProvider) {
        echo "✅ OpenRouter provider found:\n";
        echo "   " . json_encode($openrouterProvider) . "\n\n";
        
        // Check OpenRouter API keys
        $stmt = $pdo->prepare("SELECT * FROM ai_api_keys WHERE provider_id = ?");
        $stmt->execute([$openrouterProvider['id']]);
        $apiKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "OpenRouter API keys:\n";
        if (empty($apiKeys)) {
            echo "❌ No API keys found for OpenRouter\n";
        } else {
            foreach ($apiKeys as $key) {
                $maskedKey = substr($key['api_key'], 0, 8) . '...' . substr($key['api_key'], -4);
                echo "   • ID: {$key['id']}, Name: {$key['key_name']}, Active: {$key['is_active']}, Key: $maskedKey\n";
            }
        }
        
        // Check OpenRouter models
        $stmt = $pdo->prepare("SELECT * FROM ai_models WHERE provider_id = ?");
        $stmt->execute([$openrouterProvider['id']]);
        $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\nOpenRouter models:\n";
        if (empty($models)) {
            echo "❌ No models found for OpenRouter\n";
        } else {
            foreach ($models as $model) {
                echo "   • ID: {$model['id']}, Name: {$model['name']}, Model ID: {$model['model_id']}, Active: {$model['is_active']}\n";
            }
        }
        
    } else {
        echo "❌ OpenRouter provider not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== CHECK COMPLETE ===\n";
?>
