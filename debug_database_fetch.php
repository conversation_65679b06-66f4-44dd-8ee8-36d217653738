<?php
/**
 * Debug Database Fetch
 * 
 * This script tests the exact database queries and fetching logic.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

header('Content-Type: text/plain');

echo "=== DEBUGGING DATABASE FETCH ===\n\n";

try {
    // Test direct PDO connection first
    echo "1. Testing direct PDO connection...\n";
    $pdo = get_db_connection();
    echo "✅ PDO connection successful\n";
    
    // Test direct PDO query
    echo "\n2. Testing direct PDO queries...\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_models");
    $stmt->execute();
    $modelsCount = $stmt->fetchColumn();
    echo "Direct PDO - Models count: $modelsCount\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers");
    $stmt->execute();
    $providersCount = $stmt->fetchColumn();
    echo "Direct PDO - Providers count: $providersCount\n";
    
    // Test Database class methods
    echo "\n3. Testing Database class methods...\n";
    
    $dbModelsCount = $db->getValue("SELECT COUNT(*) FROM ai_models");
    echo "Database class - Models count: " . ($dbModelsCount ?? 'NULL') . "\n";
    
    $dbProvidersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers");
    echo "Database class - Providers count: " . ($dbProvidersCount ?? 'NULL') . "\n";
    
    // Test the exact queries from the template
    echo "\n4. Testing exact template queries...\n";
    
    // Test table existence check
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    echo "SHOW TABLES LIKE 'ai_providers': " . (empty($tableExists) ? 'EMPTY' : 'FOUND') . "\n";
    if (!empty($tableExists)) {
        echo "Result: " . print_r($tableExists, true) . "\n";
    }
    
    $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");
    echo "SHOW TABLES LIKE 'ai_models': " . (empty($modelsTableExists) ? 'EMPTY' : 'FOUND') . "\n";
    
    // Test providers query
    echo "\n5. Testing providers query...\n";
    $providers = $db->query("SELECT * FROM ai_providers ORDER BY name");
    echo "Providers query result count: " . count($providers) . "\n";
    if (!empty($providers)) {
        echo "First provider: " . print_r($providers[0], true) . "\n";
    }
    
    // Test models query step by step
    echo "\n6. Testing models queries step by step...\n";
    
    // Query 1: Active providers only
    echo "Query 1 - Active providers only:\n";
    $query1 = "
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 1
        ORDER BY p.name, m.name
    ";
    echo "SQL: $query1\n";
    $models1 = $db->query($query1);
    echo "Result count: " . count($models1) . "\n";
    if (!empty($models1)) {
        echo "First model: " . print_r($models1[0], true) . "\n";
    }
    
    // Query 2: All providers
    echo "\nQuery 2 - All providers:\n";
    $query2 = "
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        ORDER BY p.name, m.name
    ";
    echo "SQL: $query2\n";
    $models2 = $db->query($query2);
    echo "Result count: " . count($models2) . "\n";
    if (!empty($models2)) {
        echo "First model: " . print_r($models2[0], true) . "\n";
    }
    
    // Query 3: Orphaned models
    echo "\nQuery 3 - Orphaned models:\n";
    $query3 = "
        SELECT m.*, 'Unknown Provider' as provider_name
        FROM ai_models m
        LEFT JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.id IS NULL
        ORDER BY m.name
    ";
    echo "SQL: $query3\n";
    $models3 = $db->query($query3);
    echo "Result count: " . count($models3) . "\n";
    if (!empty($models3)) {
        echo "First orphaned model: " . print_r($models3[0], true) . "\n";
    }
    
    // Test direct SQL with PDO to compare
    echo "\n7. Testing same queries with direct PDO...\n";
    
    $stmt = $pdo->prepare($query1);
    $stmt->execute();
    $pdoModels1 = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "PDO Query 1 result count: " . count($pdoModels1) . "\n";
    
    $stmt = $pdo->prepare($query2);
    $stmt->execute();
    $pdoModels2 = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "PDO Query 2 result count: " . count($pdoModels2) . "\n";
    
    // Show raw data
    echo "\n8. Raw data inspection...\n";
    
    $stmt = $pdo->prepare("SELECT id, name, provider_id, is_active FROM ai_models");
    $stmt->execute();
    $rawModels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Raw models:\n";
    foreach ($rawModels as $model) {
        echo "   ID: {$model['id']}, Name: {$model['name']}, Provider ID: {$model['provider_id']}, Active: {$model['is_active']}\n";
    }
    
    $stmt = $pdo->prepare("SELECT id, name, is_active FROM ai_providers");
    $stmt->execute();
    $rawProviders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "\nRaw providers:\n";
    foreach ($rawProviders as $provider) {
        echo "   ID: {$provider['id']}, Name: {$provider['name']}, Active: {$provider['is_active']}\n";
    }
    
    // Test the exact template logic
    echo "\n9. Simulating exact template logic...\n";
    
    // Simulate the template code
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    
    if (!empty($tableExists)) {
        echo "✅ ai_providers table exists check passed\n";
        
        $providers = $db->query("SELECT * FROM ai_providers ORDER BY name");
        echo "✅ Providers loaded: " . count($providers) . "\n";
        
        $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");
        
        if (!empty($modelsTableExists)) {
            echo "✅ ai_models table exists check passed\n";
            
            // Try the robust query logic
            try {
                $models = $db->query("
                    SELECT m.*, p.name as provider_name
                    FROM ai_models m
                    JOIN ai_providers p ON m.provider_id = p.id
                    WHERE p.is_active = 1
                    ORDER BY p.name, m.name
                ");
                
                echo "First query result: " . count($models) . " models\n";
                
                if (empty($models)) {
                    $models = $db->query("
                        SELECT m.*, p.name as provider_name
                        FROM ai_models m
                        JOIN ai_providers p ON m.provider_id = p.id
                        ORDER BY p.name, m.name
                    ");
                    echo "Second query result: " . count($models) . " models\n";
                }
                
                if (empty($models)) {
                    $orphanedModels = $db->query("
                        SELECT m.*, 'Unknown Provider' as provider_name
                        FROM ai_models m
                        LEFT JOIN ai_providers p ON m.provider_id = p.id
                        WHERE p.id IS NULL
                        ORDER BY m.name
                    ");
                    
                    if (!empty($orphanedModels)) {
                        $models = $orphanedModels;
                        echo "Third query result: " . count($models) . " models\n";
                    }
                }
                
                echo "FINAL TEMPLATE RESULT: " . count($models) . " models\n";
                
                if (!empty($models)) {
                    echo "✅ MODELS SHOULD BE VISIBLE!\n";
                    echo "Models that should appear:\n";
                    foreach ($models as $model) {
                        echo "   • {$model['provider_name']} / {$model['name']}\n";
                    }
                } else {
                    echo "❌ NO MODELS WILL BE DISPLAYED\n";
                }
                
            } catch (Exception $e) {
                echo "❌ Exception in template logic: " . $e->getMessage() . "\n";
                
                // Try fallback
                $fallbackModels = $db->query("SELECT *, 'Unknown Provider' as provider_name FROM ai_models ORDER BY name");
                echo "Fallback query result: " . count($fallbackModels) . " models\n";
            }
        } else {
            echo "❌ ai_models table check failed\n";
        }
    } else {
        echo "❌ ai_providers table check failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
?>
