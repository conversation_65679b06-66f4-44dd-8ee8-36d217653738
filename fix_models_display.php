<?php
/**
 * Fix Models Display Issue
 * 
 * This script specifically fixes the issue where models exist in database but don't show in the list.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

header('Content-Type: text/plain');

echo "=== FIXING MODELS DISPLAY ISSUE ===\n\n";

try {
    $fixesApplied = 0;
    
    // 1. Check current state
    echo "1. Checking current state...\n";
    $modelsCount = $db->getValue("SELECT COUNT(*) FROM ai_models");
    $providersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers");
    $activeProvidersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    
    echo "Total models: $modelsCount\n";
    echo "Total providers: $providersCount\n";
    echo "Active providers: $activeProvidersCount\n\n";
    
    // 2. Test the JOIN query
    echo "2. Testing JOIN query...\n";
    $joinResults = $db->query("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        ORDER BY p.name, m.name
    ");
    echo "JOIN query results: " . count($joinResults) . "\n\n";
    
    if (count($joinResults) > 0) {
        echo "✅ Models are visible! The issue might be elsewhere.\n";
        echo "Visible models:\n";
        foreach ($joinResults as $model) {
            echo "   • {$model['provider_name']} / {$model['name']}\n";
        }
        echo "\nIf you still don't see models in the interface, try refreshing the page.\n";
        exit;
    }
    
    // 3. If no results, find and fix the issues
    echo "3. No models visible. Finding issues...\n\n";
    
    // Check for models with invalid provider_id
    echo "3a. Checking for models with invalid provider_id...\n";
    $orphanedModels = $db->query("
        SELECT m.* 
        FROM ai_models m 
        WHERE m.provider_id NOT IN (SELECT id FROM ai_providers)
    ");
    
    if (!empty($orphanedModels)) {
        echo "Found " . count($orphanedModels) . " orphaned models.\n";
        
        // Get or create a default provider
        $defaultProvider = $db->getRow("SELECT id, name FROM ai_providers WHERE is_active = 1 ORDER BY id LIMIT 1");
        
        if (!$defaultProvider) {
            echo "No active providers found. Creating default provider...\n";
            $providerId = $db->insert('ai_providers', [
                'name' => 'Default AI Provider',
                'slug' => 'default',
                'description' => 'Default provider for orphaned models',
                'is_active' => 1
            ]);
            $defaultProvider = ['id' => $providerId, 'name' => 'Default AI Provider'];
            echo "Created default provider (ID: $providerId)\n";
            $fixesApplied++;
        }
        
        // Reassign orphaned models
        foreach ($orphanedModels as $model) {
            $db->update('ai_models', 
                ['provider_id' => $defaultProvider['id']], 
                'id = ?', 
                [$model['id']]
            );
            echo "   • Fixed model: {$model['name']} -> {$defaultProvider['name']}\n";
            $fixesApplied++;
        }
    } else {
        echo "No orphaned models found.\n";
    }
    
    // Check for models with inactive providers
    echo "\n3b. Checking for models with inactive providers...\n";
    $modelsWithInactiveProviders = $db->query("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 0
    ");
    
    if (!empty($modelsWithInactiveProviders)) {
        echo "Found " . count($modelsWithInactiveProviders) . " models with inactive providers.\n";
        
        // Activate providers that have models
        $inactiveProviders = $db->query("
            SELECT DISTINCT p.id, p.name
            FROM ai_providers p
            JOIN ai_models m ON p.id = m.provider_id
            WHERE p.is_active = 0
        ");
        
        foreach ($inactiveProviders as $provider) {
            $db->update('ai_providers', 
                ['is_active' => 1], 
                'id = ?', 
                [$provider['id']]
            );
            echo "   • Activated provider: {$provider['name']}\n";
            $fixesApplied++;
        }
    } else {
        echo "No models with inactive providers found.\n";
    }
    
    // 4. Ensure we have at least one active provider
    echo "\n4. Ensuring active providers exist...\n";
    $activeProviders = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    
    if ($activeProviders == 0) {
        echo "No active providers found. Creating default providers...\n";
        
        // Create essential providers
        $defaultProviders = [
            [
                'name' => 'OpenRouter',
                'slug' => 'openrouter',
                'description' => 'Access to multiple AI models through a unified API',
                'website' => 'https://openrouter.ai',
                'api_base_url' => 'https://openrouter.ai/api/v1',
                'auth_type' => 'bearer',
                'is_active' => 1
            ],
            [
                'name' => 'NOVITA AI',
                'slug' => 'novita',
                'description' => 'AI platform providing text and image generation',
                'website' => 'https://novita.ai',
                'api_base_url' => 'https://api.novita.ai/v1',
                'auth_type' => 'bearer',
                'is_active' => 1
            ]
        ];
        
        foreach ($defaultProviders as $providerData) {
            $existingProvider = $db->getRow("SELECT id FROM ai_providers WHERE slug = ?", [$providerData['slug']]);
            
            if ($existingProvider) {
                // Update existing provider to be active
                $db->update('ai_providers', ['is_active' => 1], 'id = ?', [$existingProvider['id']]);
                echo "   • Activated existing provider: {$providerData['name']}\n";
            } else {
                // Create new provider
                $providerId = $db->insert('ai_providers', $providerData);
                echo "   • Created provider: {$providerData['name']} (ID: $providerId)\n";
            }
            $fixesApplied++;
        }
    } else {
        echo "Found $activeProviders active providers.\n";
    }
    
    // 5. Final verification
    echo "\n5. Final verification...\n";
    $finalResults = $db->query("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 1
        ORDER BY p.name, m.name
    ");
    
    echo "Final visible models count: " . count($finalResults) . "\n";
    
    if (!empty($finalResults)) {
        echo "✅ SUCCESS! Models are now visible:\n";
        foreach ($finalResults as $model) {
            echo "   • {$model['provider_name']} / {$model['name']}\n";
        }
    } else {
        echo "❌ Still no visible models. Manual intervention may be required.\n";
        
        // Show what we have
        echo "\nCurrent database state:\n";
        $allModels = $db->query("SELECT id, name, provider_id FROM ai_models");
        $allProviders = $db->query("SELECT id, name, is_active FROM ai_providers");
        
        echo "Models:\n";
        foreach ($allModels as $m) {
            echo "   • ID: {$m['id']}, Name: {$m['name']}, Provider ID: {$m['provider_id']}\n";
        }
        
        echo "Providers:\n";
        foreach ($allProviders as $p) {
            $status = $p['is_active'] ? 'ACTIVE' : 'INACTIVE';
            echo "   • ID: {$p['id']}, Name: {$p['name']}, Status: $status\n";
        }
    }
    
    // 6. Summary
    echo "\n=== SUMMARY ===\n";
    echo "Fixes applied: $fixesApplied\n";
    
    if ($fixesApplied > 0) {
        echo "✅ Issues have been fixed. Please refresh the AI Settings > Models page.\n";
    } else {
        echo "ℹ️  No fixes were needed. The issue might be elsewhere.\n";
    }
    
    echo "\nNext steps:\n";
    echo "1. Refresh the AI Settings > Models page\n";
    echo "2. If models still don't appear, check browser console for JavaScript errors\n";
    echo "3. Try adding a new model to test the functionality\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== FIX COMPLETE ===\n";
?>
