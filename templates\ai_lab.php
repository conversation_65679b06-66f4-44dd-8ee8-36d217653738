<?php
/**
 * AI Lab - Modern Chat Interface
 *
 * A beautiful 2-way chat interface for testing AI models
 */

// No AJAX handling here - using dedicated API endpoint

// Check if user is logged in (this check is already done in index.php, so we can remove it)
// if (!isset($_SESSION['user_id'])) {
//     header('Location: ' . BASE_URL . '/login.php');
//     exit;
// }

// Initialize AI Lab Manager
require_once 'includes/AILabManager.php';
$labManager = new AILabManager($db, $_SESSION['user_id']);

// Get providers and models for model selector using direct PDO
try {
    $pdo = get_db_connection();

    // Get providers
    $stmt = $pdo->prepare("SELECT * FROM ai_providers WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    $providers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get models with robust query
    $stmt = $pdo->prepare("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE m.is_active = 1 AND p.is_active = 1
        ORDER BY p.name, m.name
    ");
    $stmt->execute();
    $models = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // If no models with active providers, try all models
    if (empty($models)) {
        $stmt = $pdo->prepare("
            SELECT m.*, p.name as provider_name
            FROM ai_models m
            JOIN ai_providers p ON m.provider_id = p.id
            ORDER BY p.name, m.name
        ");
        $stmt->execute();
        $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

} catch (Exception $e) {
    // Fallback to Database class
    $providers = $db->query("SELECT * FROM ai_providers WHERE is_active = 1 ORDER BY name");
    $models = $db->query("SELECT m.*, p.name as provider_name FROM ai_models m JOIN ai_providers p ON m.provider_id = p.id WHERE m.is_active = 1 ORDER BY p.name, m.name");
}

// Get lab statistics
$stats = $labManager->getLabStatistics();
?>

<!-- AI Lab Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="fas fa-flask text-primary me-2"></i>AI Lab
        </h1>
        <p class="text-muted mb-0">Test and experiment with AI models</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearChat()">
            <i class="fas fa-trash me-1"></i> Clear Chat
        </button>
        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#statsModal">
            <i class="fas fa-chart-bar me-1"></i> Stats
        </button>
    </div>
</div>

<!-- Main Chat Interface -->
<div class="row g-0" style="height: calc(100vh - 200px);">
    <!-- Model Selector Sidebar -->
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI Models
                </h6>
            </div>
            <div class="card-body p-0">
                <!-- Provider Tabs -->
                <div class="nav nav-tabs border-0" id="provider-tabs" role="tablist">
                    <?php
                    $activeProviderSet = false;
                    foreach ($providers as $index => $provider):
                        $isActive = !$activeProviderSet;
                        if ($isActive) $activeProviderSet = true;
                    ?>
                    <button class="nav-link <?php echo $isActive ? 'active' : ''; ?> border-0 rounded-0 small"
                            id="provider-<?php echo $provider['id']; ?>-tab"
                            data-bs-toggle="tab"
                            data-bs-target="#provider-<?php echo $provider['id']; ?>"
                            type="button"
                            role="tab">
                        <?php echo htmlspecialchars($provider['name']); ?>
                    </button>
                    <?php endforeach; ?>
                </div>

                <!-- Provider Content -->
                <div class="tab-content" id="provider-content">
                    <?php
                    $activeProviderSet = false;
                    foreach ($providers as $provider):
                        $isActive = !$activeProviderSet;
                        if ($isActive) $activeProviderSet = true;
                        $providerModels = array_filter($models, function($model) use ($provider) {
                            return $model['provider_id'] == $provider['id'];
                        });
                    ?>
                    <div class="tab-pane fade <?php echo $isActive ? 'show active' : ''; ?>"
                         id="provider-<?php echo $provider['id']; ?>"
                         role="tabpanel">

                        <?php if (empty($providerModels)): ?>
                        <div class="p-3 text-center text-muted">
                            <i class="fas fa-robot fa-2x mb-2"></i>
                            <p class="mb-0 small">No models available</p>
                        </div>
                        <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($providerModels as $model): ?>
                            <div class="list-group-item list-group-item-action model-item"
                                 data-provider-id="<?php echo $provider['id']; ?>"
                                 data-model-id="<?php echo $model['id']; ?>"
                                 data-provider-name="<?php echo htmlspecialchars($provider['name']); ?>"
                                 data-model-name="<?php echo htmlspecialchars($model['name']); ?>"
                                 data-model-identifier="<?php echo htmlspecialchars($model['model_id']); ?>"
                                 onclick="selectModel(this)">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 small"><?php echo htmlspecialchars($model['name']); ?></h6>
                                        <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                            <?php echo htmlspecialchars($model['model_id']); ?>
                                        </p>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-<?php echo $model['type'] === 'text' ? 'primary' : 'secondary'; ?> small">
                                            <?php echo ucfirst($model['type']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Selected Model Info -->
                <div class="border-top p-3 bg-light">
                    <div id="selected-model-info" class="text-center text-muted small">
                        <i class="fas fa-hand-pointer me-1"></i>
                        Select a model to start chatting
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Area -->
    <div class="col-md-9">
        <div class="card border-0 shadow-sm h-100">
            <!-- Chat Header -->
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                <div id="chat-header-info">
                    <h6 class="mb-0">AI Lab Chat</h6>
                    <small class="text-muted">Select a model to start chatting</small>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportChat()" disabled id="export-btn">
                        <i class="fas fa-download me-1"></i> Export
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearChat()" disabled id="clear-btn">
                        <i class="fas fa-trash me-1"></i> Clear
                    </button>
                </div>
            </div>

            <!-- Messages Container -->
            <div class="card-body p-0 d-flex flex-column">
                <div id="messages-container" class="flex-grow-1 p-4" style="height: 500px; overflow-y: auto; background: linear-gradient(to bottom, #f8f9fa, #ffffff);">
                    <!-- Welcome Message -->
                    <div id="welcome-message" class="text-center text-muted py-5">
                        <i class="fas fa-comments fa-4x mb-4 text-primary opacity-50"></i>
                        <h5>Welcome to AI Lab</h5>
                        <p class="mb-4">Select an AI model from the sidebar and start your conversation!</p>
                        <div class="row g-3 justify-content-center">
                            <div class="col-auto">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-robot text-primary mb-2"></i>
                                        <div class="small">Choose Model</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-comments text-success mb-2"></i>
                                        <div class="small">Start Chatting</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-magic text-warning mb-2"></i>
                                        <div class="small">Get Results</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages (initially hidden) -->
                    <div id="chat-messages" style="display: none;">
                        <!-- Messages will be added here dynamically -->
                    </div>

                    <!-- Typing indicator (hidden by default) -->
                    <div id="typing-indicator" class="message mb-3" style="display: none;">
                        <div class="d-inline-block p-3 rounded bg-white border shadow-sm">
                            <div class="typing-animation">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            <small class="text-muted ms-2">AI is thinking...</small>
                        </div>
                    </div>
                </div>

                <!-- Message Input -->
                <div class="border-top p-3 bg-light">
                    <form id="message-form" onsubmit="sendMessage(event)">
                        <div class="input-group">
                            <textarea class="form-control border-0 shadow-sm"
                                      id="message-input"
                                      placeholder="Select a model and type your message... (Shift+Enter for new line, Enter to send)"
                                      rows="2"
                                      disabled
                                      style="resize: none; max-height: 120px;"></textarea>
                            <button class="btn btn-primary shadow-sm" type="submit" id="send-button" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="small text-muted mt-2 d-flex justify-content-between">
                            <span>
                                <span id="char-count">0</span> characters
                            </span>
                            <span id="model-info">
                                No model selected
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Modal -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">AI Lab Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="card border-0 bg-primary bg-opacity-10">
                            <div class="card-body text-center">
                                <i class="fas fa-comments fa-2x text-primary mb-2"></i>
                                <h4 class="mb-0"><?php echo number_format($stats['total_conversations']); ?></h4>
                                <small class="text-muted">Conversations</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card border-0 bg-success bg-opacity-10">
                            <div class="card-body text-center">
                                <i class="fas fa-message fa-2x text-success mb-2"></i>
                                <h4 class="mb-0"><?php echo number_format($stats['total_messages']); ?></h4>
                                <small class="text-muted">Messages</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card border-0 bg-warning bg-opacity-10">
                            <div class="card-body text-center">
                                <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                                <h4 class="mb-0"><?php echo number_format($stats['total_tokens']); ?></h4>
                                <small class="text-muted">Tokens Used</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card border-0 bg-info bg-opacity-10">
                            <div class="card-body text-center">
                                <i class="fas fa-robot fa-2x text-info mb-2"></i>
                                <h6 class="mb-0"><?php echo $stats['most_used_provider']['name'] ?? 'None'; ?></h6>
                                <small class="text-muted">Top Provider</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let currentProviderId = null;
let currentModelId = null;
let currentProviderName = null;
let currentModelName = null;
let currentModelIdentifier = null;
let chatHistory = [];

// Select model function
function selectModel(element) {
    console.log('selectModel called', element);

    // Remove active class from all models
    document.querySelectorAll('.model-item').forEach(item => {
        item.classList.remove('active', 'bg-primary', 'text-white');
    });

    // Add active class to selected model
    element.classList.add('active', 'bg-primary', 'text-white');

    // Get model data
    currentProviderId = element.dataset.providerId;
    currentModelId = element.dataset.modelId;
    currentProviderName = element.dataset.providerName;
    currentModelName = element.dataset.modelName;
    currentModelIdentifier = element.dataset.modelIdentifier;

    console.log('Model selected:', {
        currentProviderId,
        currentModelId,
        currentProviderName,
        currentModelName,
        currentModelIdentifier
    });

    // Update UI
    updateSelectedModelInfo();
    updateChatHeader();
    enableChatInput();
    showChatArea();
}

// Update selected model info
function updateSelectedModelInfo() {
    const infoElement = document.getElementById('selected-model-info');
    if (infoElement) {
        infoElement.innerHTML = `
            <div class="text-start">
                <div class="fw-bold text-primary">${currentModelName}</div>
                <div class="small text-muted">${currentProviderName}</div>
                <div class="small text-muted">${currentModelIdentifier}</div>
            </div>
        `;
    }
}

// Update chat header
function updateChatHeader() {
    const headerElement = document.getElementById('chat-header-info');
    if (headerElement) {
        headerElement.innerHTML = `
            <h6 class="mb-0">${currentProviderName} / ${currentModelName}</h6>
            <small class="text-muted">${currentModelIdentifier}</small>
        `;
    }

    const modelInfoElement = document.getElementById('model-info');
    if (modelInfoElement) {
        modelInfoElement.textContent = `${currentProviderName} / ${currentModelName}`;
    }
}

// Enable chat input
function enableChatInput() {
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const exportBtn = document.getElementById('export-btn');
    const clearBtn = document.getElementById('clear-btn');

    if (messageInput) {
        messageInput.disabled = false;
        messageInput.placeholder = "Type your message... (Shift+Enter for new line, Enter to send)";
        messageInput.focus();
    }

    if (sendButton) sendButton.disabled = false;
    if (exportBtn) exportBtn.disabled = false;
    if (clearBtn) clearBtn.disabled = false;
}

// Show chat area
function showChatArea() {
    const welcomeMessage = document.getElementById('welcome-message');
    const chatMessages = document.getElementById('chat-messages');

    if (welcomeMessage) welcomeMessage.style.display = 'none';
    if (chatMessages) chatMessages.style.display = 'block';
}

// Send message
function sendMessage(event) {
    event.preventDefault();

    console.log('sendMessage called', {
        currentModelId,
        currentProviderId,
        currentModelName
    });

    if (!currentModelId) {
        alert('Please select a model first');
        return;
    }

    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();

    if (!message) {
        return;
    }

    // Disable input and button
    const sendButton = document.getElementById('send-button');
    messageInput.disabled = true;
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Add user message to chat immediately
    addMessageToChat('user', message);
    messageInput.value = '';
    updateCharCount();

    // Show typing indicator
    showTypingIndicator();

    // Send to server (using debug endpoint)
    fetch('<?php echo BASE_URL; ?>/api/ai_lab_debug.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'send_message_direct',
            provider_id: currentProviderId,
            model_id: currentModelId,
            message: message,
            conversation_history: JSON.stringify(chatHistory)
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        return response.text().then(text => {
            console.log('Raw response text:', text);

            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                throw new Error('Invalid JSON response: ' + text.substring(0, 200) + '...');
            }
        });
    })
    .then(data => {
        console.log('Parsed response data:', data);

        // Hide typing indicator
        hideTypingIndicator();

        if (data.success) {
            // Add AI response to chat
            addMessageToChat('assistant', data.response, {
                tokens_used: data.tokens_used,
                execution_time: data.execution_time,
                model_used: data.model_used
            });

            // Log debug info if available
            if (data.debug_info) {
                console.log('Debug info:', data.debug_info);
            }
        } else {
            // Add error message with more details
            let errorMsg = 'Error: ' + data.error;
            if (data.trace) {
                console.error('Error trace:', data.trace);
            }
            if (data.log_data) {
                console.log('Log data:', data.log_data);
            }
            addMessageToChat('assistant', errorMsg, { error: true });
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        hideTypingIndicator();
        addMessageToChat('assistant', 'Network error: ' + error.message, { error: true });
    })
    .finally(() => {
        // Re-enable input and button
        messageInput.disabled = false;
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        messageInput.focus();
    });
}

// Add message to chat interface
function addMessageToChat(role, content, metadata = {}) {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message mb-3' + (role === 'user' ? ' text-end' : '');

    let messageClass = role === 'user' ? 'bg-primary text-white' : 'bg-white border';
    if (metadata.error) {
        messageClass = 'bg-danger text-white';
    }

    let metadataHtml = '';
    if (role === 'assistant' && !metadata.error) {
        metadataHtml = '<div class="small text-muted mt-2 border-top pt-2">';
        if (metadata.tokens_used) {
            metadataHtml += '<span class="me-3"><i class="fas fa-coins me-1"></i>' + metadata.tokens_used.toLocaleString() + ' tokens</span>';
        }
        if (metadata.execution_time) {
            metadataHtml += '<span class="me-3"><i class="fas fa-clock me-1"></i>' + metadata.execution_time.toFixed(2) + 's</span>';
        }
        if (metadata.model_used) {
            metadataHtml += '<span><i class="fas fa-robot me-1"></i>' + metadata.model_used + '</span>';
        }
        metadataHtml += '</div>';
    }

    const timestamp = new Date().toLocaleString([], {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute:'2-digit'
    });

    messageDiv.innerHTML = `
        <div class="d-inline-block p-3 rounded shadow-sm ${messageClass}" style="max-width: 85%;">
            ${metadata.error ? '<i class="fas fa-exclamation-triangle me-1"></i><strong>Error:</strong> ' : ''}
            <div class="message-content">${content.replace(/\n/g, '<br>')}</div>
            ${metadataHtml}
        </div>
        <div class="small text-muted mt-1 ${role === 'user' ? 'text-end' : ''}">
            <i class="fas fa-${role === 'user' ? 'user' : 'robot'} me-1"></i>
            ${timestamp}
        </div>
    `;

    chatMessages.appendChild(messageDiv);

    // Add to chat history
    chatHistory.push({
        role: role,
        content: content
    });

    scrollToBottom();
}

// Show typing indicator
function showTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.style.display = 'block';
        scrollToBottom();
    }
}

// Hide typing indicator
function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.style.display = 'none';
    }
}

// Scroll to bottom of messages
function scrollToBottom() {
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

// Update character count
function updateCharCount() {
    const messageInput = document.getElementById('message-input');
    const charCount = document.getElementById('char-count');
    if (messageInput && charCount) {
        charCount.textContent = messageInput.value.length;
    }
}

// Clear chat
function clearChat() {
    if (confirm('Are you sure you want to clear the chat history?')) {
        const chatMessages = document.getElementById('chat-messages');
        const welcomeMessage = document.getElementById('welcome-message');

        if (chatMessages) chatMessages.innerHTML = '';
        if (welcomeMessage) welcomeMessage.style.display = 'block';
        if (chatMessages) chatMessages.style.display = 'none';

        chatHistory = [];
    }
}

// Export chat
function exportChat() {
    if (chatHistory.length === 0) {
        alert('No chat history to export');
        return;
    }

    let exportText = `AI Lab Chat Export\n`;
    exportText += `Model: ${currentProviderName} / ${currentModelName}\n`;
    exportText += `Date: ${new Date().toLocaleString()}\n`;
    exportText += `\n${'='.repeat(50)}\n\n`;

    chatHistory.forEach((msg, index) => {
        const role = msg.role === 'user' ? 'You' : 'AI';
        exportText += `${role}: ${msg.content}\n\n`;
    });

    const blob = new Blob([exportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-lab-chat-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        // Update character count on input
        messageInput.addEventListener('input', updateCharCount);

        // Handle Enter key in message input (Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.getElementById('message-form').dispatchEvent(new Event('submit'));
            }
        });

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    }

    // Initialize character count
    updateCharCount();
});
</script>

<style>
/* Typing animation */
.typing-animation {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
}

.typing-animation span {
    height: 8px;
    width: 8px;
    background-color: #6c757d;
    border-radius: 50%;
    display: inline-block;
    margin-right: 3px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-animation span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Message animations */
.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Model selection hover effects */
.model-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

.model-item:hover {
    background-color: #f8f9fa !important;
    transform: translateX(5px);
}

.model-item.active {
    transform: translateX(0) !important;
}

/* Scrollbar styling */
#messages-container::-webkit-scrollbar {
    width: 6px;
}

#messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message input focus effect */
#message-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Hover effects for messages */
.message:hover .message-content {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}

/* Provider tabs styling */
.nav-tabs .nav-link {
    border: none !important;
    background: transparent;
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.nav-tabs .nav-link.active {
    background: #e9ecef !important;
    color: #495057;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    background: #f8f9fa;
    color: #495057;
}

/* Chat header styling */
.card-header {
    border-bottom: 1px solid #dee2e6 !important;
}

/* Welcome message styling */
#welcome-message .card {
    transition: transform 0.2s ease;
}

#welcome-message .card:hover {
    transform: translateY(-2px);
}

/* Button styling */
.btn-sm {
    font-size: 0.8rem;
}

/* Selected model info styling */
#selected-model-info {
    border-radius: 0.375rem;
    background: #f8f9fa;
}
</style>
