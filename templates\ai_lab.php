<?php
// Set page title
$pageTitle = 'AI Lab';

// Load required files
require_once 'includes/AILabManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . BASE_URL . '/login.php');
    exit;
}

// Initialize lab manager
$labManager = new AILabManager($db, $_SESSION['user_id']);

// Get action
$action = $_GET['action'] ?? '';
$conversationId = isset($_GET['conversation_id']) ? (int)$_GET['conversation_id'] : 0;

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'send_message':
                $conversationId = (int)$_POST['conversation_id'];
                $message = $_POST['message'] ?? '';

                if (empty($message)) {
                    throw new Exception('Message cannot be empty');
                }

                $result = $labManager->sendMessage($conversationId, $message);
                echo json_encode($result);
                break;

            case 'create_conversation':
                $providerId = (int)$_POST['provider_id'];
                $modelId = (int)$_POST['model_id'];
                $title = $_POST['title'] ?? null;

                $conversationId = $labManager->createConversation($providerId, $modelId, $title);
                echo json_encode(['success' => true, 'conversation_id' => $conversationId]);
                break;

            case 'delete_conversation':
                $conversationId = (int)$_POST['conversation_id'];
                $labManager->deleteConversation($conversationId);
                echo json_encode(['success' => true]);
                break;

            case 'update_title':
                $conversationId = (int)$_POST['conversation_id'];
                $title = $_POST['title'] ?? '';

                $labManager->updateConversationTitle($conversationId, $title);
                echo json_encode(['success' => true]);
                break;

            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get conversations
$conversations = $labManager->getUserConversations();

// Get current conversation if specified
$currentConversation = null;
$messages = [];
if ($conversationId > 0) {
    $currentConversation = $labManager->getConversation($conversationId);
    if ($currentConversation) {
        $messages = $labManager->getConversationMessages($conversationId);
    }
}

// Get providers and models for new conversation
$providers = $db->query("SELECT * FROM ai_providers WHERE is_active = 1 ORDER BY name");
$models = $db->query("SELECT m.*, p.name as provider_name FROM ai_models m JOIN ai_providers p ON m.provider_id = p.id WHERE m.is_active = 1 ORDER BY p.name, m.name");

// Get lab statistics
$stats = $labManager->getLabStatistics();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-flask text-primary me-2"></i>AI Lab
    </h1>

    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newConversationModal">
            <i class="fas fa-plus me-1"></i> New Conversation
        </button>
    </div>
</div>

<!-- Lab Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="icon-box bg-primary bg-opacity-10 text-primary rounded-circle p-3 mx-auto mb-2" style="width: 60px; height: 60px;">
                    <i class="fas fa-comments fa-lg"></i>
                </div>
                <h5 class="mb-1"><?php echo number_format($stats['total_conversations']); ?></h5>
                <small class="text-muted">Conversations</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="icon-box bg-success bg-opacity-10 text-success rounded-circle p-3 mx-auto mb-2" style="width: 60px; height: 60px;">
                    <i class="fas fa-message fa-lg"></i>
                </div>
                <h5 class="mb-1"><?php echo number_format($stats['total_messages']); ?></h5>
                <small class="text-muted">Messages</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="icon-box bg-warning bg-opacity-10 text-warning rounded-circle p-3 mx-auto mb-2" style="width: 60px; height: 60px;">
                    <i class="fas fa-coins fa-lg"></i>
                </div>
                <h5 class="mb-1"><?php echo number_format($stats['total_tokens']); ?></h5>
                <small class="text-muted">Tokens Used</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="icon-box bg-info bg-opacity-10 text-info rounded-circle p-3 mx-auto mb-2" style="width: 60px; height: 60px;">
                    <i class="fas fa-robot fa-lg"></i>
                </div>
                <h5 class="mb-1"><?php echo $stats['most_used_provider']['name'] ?? 'None'; ?></h5>
                <small class="text-muted">Top Provider</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Conversations Sidebar -->
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Conversations</h5>
                <span class="badge bg-primary"><?php echo count($conversations); ?></span>
            </div>
            <div class="card-body p-0">
                <?php if (empty($conversations)): ?>
                <div class="p-4 text-center">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <p class="text-muted mb-0">No conversations yet. Start your first AI test conversation!</p>
                </div>
                <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($conversations as $conv): ?>
                    <a href="<?php echo BASE_URL; ?>/?page=ai_lab&conversation_id=<?php echo $conv['id']; ?>"
                       class="list-group-item list-group-item-action <?php echo $conv['id'] == $conversationId ? 'active' : ''; ?>">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1"><?php echo htmlspecialchars($conv['title']); ?></h6>
                            <small><?php echo date('M j', strtotime($conv['updated_at'])); ?></small>
                        </div>
                        <p class="mb-1 small text-muted">
                            <i class="fas fa-robot me-1"></i><?php echo htmlspecialchars($conv['provider_name']); ?> / <?php echo htmlspecialchars($conv['model_name']); ?>
                        </p>
                        <small class="text-muted"><?php echo $conv['message_count']; ?> messages</small>
                    </a>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Chat Interface -->
    <div class="col-md-8">
        <?php if ($currentConversation): ?>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0" id="conversation-title"><?php echo htmlspecialchars($currentConversation['title']); ?></h5>
                    <small class="text-muted">
                        <i class="fas fa-robot me-1"></i><?php echo htmlspecialchars($currentConversation['provider_name']); ?> /
                        <?php echo htmlspecialchars($currentConversation['model_name']); ?>
                    </small>
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="editConversationTitle()"><i class="fas fa-edit me-1"></i> Edit Title</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteConversation()"><i class="fas fa-trash me-1"></i> Delete</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- Messages Container -->
                <div id="messages-container" class="p-3" style="height: 400px; overflow-y: auto;">
                    <?php foreach ($messages as $message): ?>
                    <div class="message mb-3 <?php echo $message['role'] === 'user' ? 'text-end' : ''; ?>">
                        <div class="d-inline-block p-3 rounded <?php echo $message['role'] === 'user' ? 'bg-primary text-white' : 'bg-light'; ?>" style="max-width: 80%;">
                            <?php if (!empty($message['error_message'])): ?>
                            <div class="text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <?php echo htmlspecialchars($message['content']); ?>
                            </div>
                            <?php else: ?>
                            <?php echo nl2br(htmlspecialchars($message['content'])); ?>
                            <?php endif; ?>

                            <?php if ($message['role'] === 'assistant' && empty($message['error_message'])): ?>
                            <div class="small text-muted mt-2">
                                <?php if ($message['tokens_used']): ?>
                                <i class="fas fa-coins me-1"></i><?php echo number_format($message['tokens_used']); ?> tokens
                                <?php endif; ?>
                                <?php if ($message['execution_time']): ?>
                                <i class="fas fa-clock ms-2 me-1"></i><?php echo number_format($message['execution_time'], 2); ?>s
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="small text-muted mt-1">
                            <?php echo date('H:i', strtotime($message['created_at'])); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Message Input -->
                <div class="border-top p-3">
                    <form id="message-form" onsubmit="sendMessage(event)">
                        <div class="input-group">
                            <textarea class="form-control" id="message-input" placeholder="Type your message..." rows="2" required></textarea>
                            <button class="btn btn-primary" type="submit" id="send-button">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <i class="fas fa-flask fa-4x text-muted mb-4"></i>
                <h4>Welcome to AI Lab</h4>
                <p class="text-muted mb-4">Test and experiment with different AI models and providers. Select a conversation from the sidebar or create a new one to get started.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newConversationModal">
                    <i class="fas fa-plus me-1"></i> Start New Conversation
                </button>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- New Conversation Modal -->
<div class="modal fade" id="newConversationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">New Lab Conversation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="new-conversation-form" onsubmit="createConversation(event)">
                    <div class="mb-3">
                        <label for="conversation-title" class="form-label">Title (Optional)</label>
                        <input type="text" class="form-control" id="conversation-title-input" placeholder="Enter conversation title">
                    </div>

                    <div class="mb-3">
                        <label for="provider-select" class="form-label">AI Provider</label>
                        <select class="form-select" id="provider-select" required onchange="updateModels()">
                            <option value="">Select a provider</option>
                            <?php foreach ($providers as $provider): ?>
                            <option value="<?php echo $provider['id']; ?>"><?php echo htmlspecialchars($provider['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="model-select" class="form-label">AI Model</label>
                        <select class="form-select" id="model-select" required disabled>
                            <option value="">Select a model</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="new-conversation-form" class="btn btn-primary">Create Conversation</button>
            </div>
        </div>
    </div>
</div>

<script>
// Models data for dynamic loading
const modelsData = <?php echo json_encode($models); ?>;
const currentConversationId = <?php echo $conversationId; ?>;

// Update models based on selected provider
function updateModels() {
    const providerSelect = document.getElementById('provider-select');
    const modelSelect = document.getElementById('model-select');
    const providerId = parseInt(providerSelect.value);

    // Clear existing options
    modelSelect.innerHTML = '<option value="">Select a model</option>';

    if (providerId) {
        // Filter models for selected provider
        const providerModels = modelsData.filter(model => model.provider_id === providerId);

        providerModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = model.name + ' (' + model.model_id + ')';
            modelSelect.appendChild(option);
        });

        modelSelect.disabled = false;
    } else {
        modelSelect.disabled = true;
    }
}

// Create new conversation
function createConversation(event) {
    event.preventDefault();

    const providerId = document.getElementById('provider-select').value;
    const modelId = document.getElementById('model-select').value;
    const title = document.getElementById('conversation-title-input').value;

    if (!providerId || !modelId) {
        alert('Please select both provider and model');
        return;
    }

    // Show loading
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Creating...';
    submitBtn.disabled = true;

    fetch('<?php echo BASE_URL; ?>/?page=ai_lab', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            ajax: '1',
            action: 'create_conversation',
            provider_id: providerId,
            model_id: modelId,
            title: title
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect to new conversation
            window.location.href = '<?php echo BASE_URL; ?>/?page=ai_lab&conversation_id=' + data.conversation_id;
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the conversation');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Send message
function sendMessage(event) {
    event.preventDefault();

    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();

    if (!message) {
        return;
    }

    // Disable input and button
    const sendButton = document.getElementById('send-button');
    messageInput.disabled = true;
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Add user message to chat immediately
    addMessageToChat('user', message);
    messageInput.value = '';

    // Send to server
    fetch('<?php echo BASE_URL; ?>/?page=ai_lab', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            ajax: '1',
            action: 'send_message',
            conversation_id: currentConversationId,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add AI response to chat
            addMessageToChat('assistant', data.response, {
                tokens_used: data.tokens_used,
                execution_time: data.execution_time,
                model_used: data.model_used
            });
        } else {
            // Add error message
            addMessageToChat('assistant', 'Error: ' + data.error, { error: true });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        addMessageToChat('assistant', 'An error occurred while sending the message', { error: true });
    })
    .finally(() => {
        // Re-enable input and button
        messageInput.disabled = false;
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        messageInput.focus();
    });
}

// Add message to chat interface
function addMessageToChat(role, content, metadata = {}) {
    const messagesContainer = document.getElementById('messages-container');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message mb-3' + (role === 'user' ? ' text-end' : '');

    let messageClass = role === 'user' ? 'bg-primary text-white' : 'bg-light';
    if (metadata.error) {
        messageClass = 'bg-danger text-white';
    }

    let metadataHtml = '';
    if (role === 'assistant' && !metadata.error) {
        metadataHtml = '<div class="small text-muted mt-2">';
        if (metadata.tokens_used) {
            metadataHtml += '<i class="fas fa-coins me-1"></i>' + metadata.tokens_used.toLocaleString() + ' tokens ';
        }
        if (metadata.execution_time) {
            metadataHtml += '<i class="fas fa-clock ms-2 me-1"></i>' + metadata.execution_time.toFixed(2) + 's';
        }
        metadataHtml += '</div>';
    }

    messageDiv.innerHTML = `
        <div class="d-inline-block p-3 rounded ${messageClass}" style="max-width: 80%;">
            ${metadata.error ? '<i class="fas fa-exclamation-triangle me-1"></i>' : ''}
            ${content.replace(/\n/g, '<br>')}
            ${metadataHtml}
        </div>
        <div class="small text-muted mt-1">
            ${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Edit conversation title
function editConversationTitle() {
    const currentTitle = document.getElementById('conversation-title').textContent;
    const newTitle = prompt('Enter new title:', currentTitle);

    if (newTitle && newTitle !== currentTitle) {
        fetch('<?php echo BASE_URL; ?>/?page=ai_lab', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ajax: '1',
                action: 'update_title',
                conversation_id: currentConversationId,
                title: newTitle
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('conversation-title').textContent = newTitle;
            } else {
                alert('Error updating title: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the title');
        });
    }
}

// Delete conversation
function deleteConversation() {
    if (confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
        fetch('<?php echo BASE_URL; ?>/?page=ai_lab', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ajax: '1',
                action: 'delete_conversation',
                conversation_id: currentConversationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '<?php echo BASE_URL; ?>/?page=ai_lab';
            } else {
                alert('Error deleting conversation: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the conversation');
        });
    }
}

// Auto-scroll messages container on page load
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Focus message input if conversation is active
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.focus();
    }

    // Handle Enter key in message input (Shift+Enter for new line)
    if (messageInput) {
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.getElementById('message-form').dispatchEvent(new Event('submit'));
            }
        });
    }
});
</script>