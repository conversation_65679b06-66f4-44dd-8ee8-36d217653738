<?php
/**
 * AI Provider Testing API
 * 
 * This script provides an API endpoint for testing AI providers and models.
 */

// Load configuration
require_once '../config.php';
require_once '../includes/Database.php';
require_once '../includes/AIProvider.php';

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Set content type
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Initialize database
$db = new Database();

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'test_connection':
            $providerId = (int)($input['provider_id'] ?? 0);
            $apiKey = $input['api_key'] ?? '';
            
            if (!$providerId || !$apiKey) {
                throw new Exception('Provider ID and API key are required');
            }
            
            // Get provider data
            $provider = $db->getRow("SELECT * FROM ai_providers WHERE id = ? AND is_active = 1", [$providerId]);
            if (!$provider) {
                throw new Exception('Provider not found or not active');
            }
            
            // Create temporary API key entry for testing
            $tempApiKeyId = $db->insert('ai_api_keys', [
                'provider_id' => $providerId,
                'name' => 'Test Key - ' . date('Y-m-d H:i:s'),
                'api_key' => $apiKey,
                'is_default' => 0,
                'is_active' => 1,
                'usage_count' => 0
            ]);
            
            try {
                // Create provider instance
                $aiProvider = AIProvider::create($db, $provider['slug'], $apiKey);
                
                // Test connection
                if (method_exists($aiProvider, 'testConnection')) {
                    $result = $aiProvider->testConnection();
                } else {
                    // Fallback test with a simple text generation
                    $testResult = $aiProvider->generateText('Hello, this is a test. Please respond with "Test successful".', [
                        'model_id' => $input['model_id'] ?? null
                    ]);
                    
                    $result = [
                        'success' => true,
                        'message' => 'Connection successful',
                        'response' => $testResult['text'] ?? 'No response',
                        'tokens_used' => $testResult['tokens_used'] ?? 0,
                        'execution_time' => $testResult['execution_time'] ?? 0
                    ];
                }
                
                echo json_encode($result);
                
            } finally {
                // Clean up temporary API key
                $db->delete('ai_api_keys', 'id = ?', [$tempApiKeyId]);
            }
            break;
            
        case 'test_model':
            $modelId = (int)($input['model_id'] ?? 0);
            $prompt = $input['prompt'] ?? 'Hello, this is a test message. Please respond briefly.';
            $apiKey = $input['api_key'] ?? '';
            
            if (!$modelId) {
                throw new Exception('Model ID is required');
            }
            
            // Get model and provider data
            $model = $db->getRow("
                SELECT m.*, p.slug as provider_slug, p.name as provider_name 
                FROM ai_models m 
                JOIN ai_providers p ON m.provider_id = p.id 
                WHERE m.id = ? AND m.is_active = 1 AND p.is_active = 1
            ", [$modelId]);
            
            if (!$model) {
                throw new Exception('Model not found or not active');
            }
            
            // Use provided API key or get default
            if ($apiKey) {
                // Create temporary API key entry for testing
                $tempApiKeyId = $db->insert('ai_api_keys', [
                    'provider_id' => $model['provider_id'],
                    'name' => 'Test Key - ' . date('Y-m-d H:i:s'),
                    'api_key' => $apiKey,
                    'is_default' => 0,
                    'is_active' => 1,
                    'usage_count' => 0
                ]);
                
                try {
                    $aiProvider = AIProvider::create($db, $model['provider_slug'], $apiKey);
                } finally {
                    // Clean up temporary API key
                    $db->delete('ai_api_keys', 'id = ?', [$tempApiKeyId]);
                }
            } else {
                $aiProvider = AIProvider::create($db, $model['provider_slug']);
            }
            
            // Test model
            $startTime = microtime(true);
            $result = $aiProvider->generateText($prompt, [
                'model_id' => $modelId,
                'max_tokens' => 150,
                'temperature' => 0.7
            ]);
            $executionTime = microtime(true) - $startTime;
            
            echo json_encode([
                'success' => true,
                'model_name' => $model['name'],
                'provider_name' => $model['provider_name'],
                'prompt' => $prompt,
                'response' => $result['text'] ?? 'No response',
                'tokens_used' => $result['tokens_used'] ?? 0,
                'execution_time' => $executionTime,
                'model_used' => $result['model'] ?? $model['model_id']
            ]);
            break;
            
        case 'get_models':
            $providerId = (int)($input['provider_id'] ?? 0);
            
            if (!$providerId) {
                throw new Exception('Provider ID is required');
            }
            
            // Get provider models
            $models = $db->query("
                SELECT id, name, model_id, type, capabilities, max_tokens, temperature, is_active
                FROM ai_models 
                WHERE provider_id = ? 
                ORDER BY name
            ", [$providerId]);
            
            // Parse capabilities JSON
            foreach ($models as &$model) {
                $model['capabilities'] = json_decode($model['capabilities'] ?? '[]', true);
            }
            
            echo json_encode([
                'success' => true,
                'models' => $models
            ]);
            break;
            
        case 'fetch_remote_models':
            $providerId = (int)($input['provider_id'] ?? 0);
            $apiKey = $input['api_key'] ?? '';
            
            if (!$providerId || !$apiKey) {
                throw new Exception('Provider ID and API key are required');
            }
            
            // Get provider data
            $provider = $db->getRow("SELECT * FROM ai_providers WHERE id = ? AND is_active = 1", [$providerId]);
            if (!$provider) {
                throw new Exception('Provider not found or not active');
            }
            
            // Create temporary API key entry
            $tempApiKeyId = $db->insert('ai_api_keys', [
                'provider_id' => $providerId,
                'name' => 'Temp Key - ' . date('Y-m-d H:i:s'),
                'api_key' => $apiKey,
                'is_default' => 0,
                'is_active' => 1,
                'usage_count' => 0
            ]);
            
            try {
                // Create provider instance
                $aiProvider = AIProvider::create($db, $provider['slug'], $apiKey);
                
                // Fetch available models if supported
                if (method_exists($aiProvider, 'getAvailableModels')) {
                    $remoteModels = $aiProvider->getAvailableModels();
                    
                    echo json_encode([
                        'success' => true,
                        'models' => $remoteModels
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'Provider does not support remote model fetching'
                    ]);
                }
                
            } finally {
                // Clean up temporary API key
                $db->delete('ai_api_keys', 'id = ?', [$tempApiKeyId]);
            }
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
