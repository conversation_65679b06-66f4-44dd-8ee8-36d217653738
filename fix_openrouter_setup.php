<?php
/**
 * Fix OpenRouter Setup
 * 
 * This script fixes common OpenRouter setup issues.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== FIXING OPENROUTER SETUP ===\n\n";

try {
    $db = new Database();
    $pdo = get_db_connection();
    
    // Step 1: Ensure OpenRouter provider exists and is active
    echo "1. Setting up OpenRouter provider...\n";
    
    $provider = $db->getRow("SELECT * FROM ai_providers WHERE slug = 'openrouter'");
    if (!$provider) {
        echo "Creating OpenRouter provider...\n";
        $providerId = $db->insert('ai_providers', [
            'name' => 'OpenRouter',
            'slug' => 'openrouter',
            'description' => 'Access to multiple AI models through a unified API',
            'website' => 'https://openrouter.ai',
            'api_base_url' => 'https://openrouter.ai/api/v1',
            'auth_type' => 'bearer',
            'is_active' => 1
        ]);
        $provider = $db->getRow("SELECT * FROM ai_providers WHERE id = ?", [$providerId]);
        echo "✅ Created OpenRouter provider (ID: $providerId)\n";
    } else {
        echo "✅ OpenRouter provider exists (ID: {$provider['id']})\n";
        
        // Ensure it's active
        if (!$provider['is_active']) {
            $db->update('ai_providers', ['is_active' => 1], 'id = ?', [$provider['id']]);
            echo "✅ Activated OpenRouter provider\n";
        }
    }
    
    // Step 2: Check and fix API key
    echo "\n2. Checking API key setup...\n";
    
    $apiKeys = $db->query("SELECT * FROM ai_api_keys WHERE provider_id = ?", [$provider['id']]);
    
    if (empty($apiKeys)) {
        echo "❌ No API key found for OpenRouter\n";
        echo "Please add your OpenRouter API key manually:\n";
        echo "1. Go to AI Settings > API Keys\n";
        echo "2. Add a new API key for OpenRouter\n";
        echo "3. Make sure it's marked as active\n";
        echo "\nOr enter your API key now:\n";
        
        // For testing, let's check if there's an API key in a common location
        $testApiKey = null;
        
        // Check if user wants to enter API key interactively (for command line)
        if (php_sapi_name() === 'cli') {
            echo "Enter your OpenRouter API key (or press Enter to skip): ";
            $handle = fopen("php://stdin", "r");
            $testApiKey = trim(fgets($handle));
            fclose($handle);
        }
        
        if ($testApiKey && strlen($testApiKey) > 10) {
            echo "Adding API key...\n";
            $apiKeyId = $db->insert('ai_api_keys', [
                'provider_id' => $provider['id'],
                'key_name' => 'Default OpenRouter Key',
                'api_key' => $testApiKey,
                'is_active' => 1,
                'is_default' => 1
            ]);
            echo "✅ API key added (ID: $apiKeyId)\n";
        } else {
            echo "⚠️  Skipping API key setup. Please add manually.\n";
        }
    } else {
        echo "✅ Found " . count($apiKeys) . " API key(s)\n";
        
        // Ensure at least one is active
        $activeKeys = array_filter($apiKeys, function($key) { return $key['is_active']; });
        if (empty($activeKeys)) {
            echo "Activating first API key...\n";
            $db->update('ai_api_keys', ['is_active' => 1], 'id = ?', [$apiKeys[0]['id']]);
            echo "✅ Activated API key\n";
        }
        
        // Ensure at least one is default
        $defaultKeys = array_filter($apiKeys, function($key) { return $key['is_default']; });
        if (empty($defaultKeys)) {
            echo "Setting first active key as default...\n";
            $firstActiveKey = $activeKeys[0] ?? $apiKeys[0];
            $db->update('ai_api_keys', ['is_default' => 1], 'id = ?', [$firstActiveKey['id']]);
            echo "✅ Set default API key\n";
        }
    }
    
    // Step 3: Ensure OpenRouter models exist
    echo "\n3. Setting up OpenRouter models...\n";
    
    $models = $db->query("SELECT * FROM ai_models WHERE provider_id = ?", [$provider['id']]);
    
    if (empty($models)) {
        echo "Creating default OpenRouter models...\n";
        
        $defaultModels = [
            [
                'name' => 'GPT-4 Turbo',
                'model_id' => 'openai/gpt-4-turbo',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ],
            [
                'name' => 'GPT-3.5 Turbo',
                'model_id' => 'openai/gpt-3.5-turbo',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ],
            [
                'name' => 'Claude 3 Sonnet',
                'model_id' => 'anthropic/claude-3-sonnet',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ],
            [
                'name' => 'Claude 3 Haiku',
                'model_id' => 'anthropic/claude-3-haiku',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ],
            [
                'name' => 'Llama 2 70B',
                'model_id' => 'meta-llama/llama-2-70b-chat',
                'type' => 'text',
                'capabilities' => json_encode(['translate', 'rewrite', 'generate']),
                'max_tokens' => 4096,
                'temperature' => 0.7,
                'is_active' => 1
            ]
        ];
        
        foreach ($defaultModels as $modelData) {
            $modelData['provider_id'] = $provider['id'];
            $modelId = $db->insert('ai_models', $modelData);
            echo "✅ Created model: {$modelData['name']} (ID: $modelId)\n";
        }
    } else {
        echo "✅ Found " . count($models) . " OpenRouter models\n";
        
        // Ensure at least one is active
        $activeModels = array_filter($models, function($model) { return $model['is_active']; });
        if (empty($activeModels)) {
            echo "Activating all models...\n";
            $db->query("UPDATE ai_models SET is_active = 1 WHERE provider_id = ?", [$provider['id']]);
            echo "✅ Activated all models\n";
        }
    }
    
    // Step 4: Test the setup
    echo "\n4. Testing OpenRouter setup...\n";
    
    // Get API key for testing
    $testApiKey = $db->getRow("SELECT * FROM ai_api_keys WHERE provider_id = ? AND is_active = 1 LIMIT 1", [$provider['id']]);
    
    if (!$testApiKey) {
        echo "❌ No active API key found for testing\n";
        echo "Please add your OpenRouter API key in AI Settings > API Keys\n";
    } else {
        echo "Testing with API key: " . substr($testApiKey['api_key'], 0, 8) . "...\n";
        
        try {
            require_once 'includes/AIProvider.php';
            $aiProvider = AIProvider::create($db, 'openrouter');
            echo "✅ AI Provider created successfully\n";
            
            // Get first model for testing
            $testModel = $db->getRow("SELECT * FROM ai_models WHERE provider_id = ? AND is_active = 1 LIMIT 1", [$provider['id']]);
            
            if ($testModel) {
                echo "Testing with model: {$testModel['name']}\n";
                
                $response = $aiProvider->generateText('Say "Hello from OpenRouter!"', [
                    'model_id' => $testModel['id'],
                    'max_tokens' => 50
                ]);
                
                echo "✅ OpenRouter test successful!\n";
                echo "Response: " . substr($response['text'], 0, 100) . "...\n";
                echo "Tokens used: {$response['tokens_used']}\n";
                echo "Execution time: " . number_format($response['execution_time'], 2) . "s\n";
            } else {
                echo "❌ No active models found for testing\n";
            }
            
        } catch (Exception $e) {
            echo "❌ OpenRouter test failed: " . $e->getMessage() . "\n";
            
            if (strpos($e->getMessage(), 'API key') !== false) {
                echo "\n🔑 API Key Issue Detected!\n";
                echo "Please check:\n";
                echo "1. Your OpenRouter API key is correct\n";
                echo "2. The API key has sufficient credits\n";
                echo "3. The API key has access to the models you're trying to use\n";
            }
        }
    }
    
    echo "\n=== SETUP SUMMARY ===\n";
    echo "✅ OpenRouter provider: Configured\n";
    echo "✅ API key: " . ($testApiKey ? 'Found' : 'Missing') . "\n";
    echo "✅ Models: " . count($models) . " available\n";
    
    if ($testApiKey) {
        echo "\n🎉 OpenRouter setup is complete!\n";
        echo "You can now use the AI Lab: " . BASE_URL . "/?page=ai_lab\n";
    } else {
        echo "\n⚠️  Setup incomplete: Please add your OpenRouter API key\n";
        echo "1. Visit: " . BASE_URL . "/?page=ai_settings&section=api_keys\n";
        echo "2. Add your OpenRouter API key\n";
        echo "3. Make sure it's marked as active\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== FIX COMPLETE ===\n";
?>
