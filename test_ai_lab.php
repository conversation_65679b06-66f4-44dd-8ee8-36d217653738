<?php
/**
 * Test AI Lab
 * 
 * This script tests the AI Lab functionality.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';
require_once 'includes/AILabManager.php';

// Start session
session_start();

// Initialize database connection
$db = new Database();

header('Content-Type: text/plain');

echo "=== TESTING AI LAB ===\n\n";

try {
    // Test 1: Check if user is logged in
    echo "1. Checking user session...\n";
    if (!isset($_SESSION['user_id'])) {
        echo "❌ No user session found. Creating test session...\n";
        $_SESSION['user_id'] = 1; // Use admin user
        echo "✅ Test session created with user ID: 1\n";
    } else {
        echo "✅ User session found: " . $_SESSION['user_id'] . "\n";
    }
    echo "\n";
    
    // Test 2: Initialize AI Lab Manager
    echo "2. Initializing AI Lab Manager...\n";
    $labManager = new AILabManager($db, $_SESSION['user_id']);
    echo "✅ AI Lab Manager initialized\n\n";
    
    // Test 3: Check providers and models
    echo "3. Checking providers and models...\n";
    
    $pdo = get_db_connection();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    $stmt->execute();
    $providersCount = $stmt->fetchColumn();
    echo "Active providers: $providersCount\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_models WHERE is_active = 1");
    $stmt->execute();
    $modelsCount = $stmt->fetchColumn();
    echo "Active models: $modelsCount\n";
    
    if ($providersCount == 0 || $modelsCount == 0) {
        echo "❌ Insufficient providers or models for testing\n";
        echo "Please run setup_default_providers.php and add some models\n";
        exit;
    }
    
    // Get first provider and model
    $stmt = $pdo->prepare("
        SELECT p.id as provider_id, p.name as provider_name, m.id as model_id, m.name as model_name
        FROM ai_providers p
        JOIN ai_models m ON p.id = m.provider_id
        WHERE p.is_active = 1 AND m.is_active = 1
        ORDER BY p.id, m.id
        LIMIT 1
    ");
    $stmt->execute();
    $testData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testData) {
        echo "❌ No valid provider-model combination found\n";
        exit;
    }
    
    echo "Test provider: {$testData['provider_name']} (ID: {$testData['provider_id']})\n";
    echo "Test model: {$testData['model_name']} (ID: {$testData['model_id']})\n\n";
    
    // Test 4: Create a test conversation
    echo "4. Testing conversation creation...\n";
    
    try {
        $conversationId = $labManager->createConversation(
            $testData['provider_id'],
            $testData['model_id'],
            'Test Conversation - ' . date('Y-m-d H:i:s')
        );
        echo "✅ Conversation created successfully with ID: $conversationId\n";
    } catch (Exception $e) {
        echo "❌ Failed to create conversation: " . $e->getMessage() . "\n";
        exit;
    }
    
    // Test 5: Get conversation details
    echo "\n5. Testing conversation retrieval...\n";
    
    try {
        $conversation = $labManager->getConversation($conversationId);
        if ($conversation) {
            echo "✅ Conversation retrieved successfully\n";
            echo "Title: {$conversation['title']}\n";
            echo "Provider: {$conversation['provider_name']}\n";
            echo "Model: {$conversation['model_name']}\n";
        } else {
            echo "❌ Failed to retrieve conversation\n";
        }
    } catch (Exception $e) {
        echo "❌ Error retrieving conversation: " . $e->getMessage() . "\n";
    }
    
    // Test 6: Get user conversations
    echo "\n6. Testing user conversations list...\n";
    
    try {
        $conversations = $labManager->getUserConversations();
        echo "✅ User conversations retrieved: " . count($conversations) . " conversations\n";
        
        if (!empty($conversations)) {
            echo "Recent conversations:\n";
            foreach (array_slice($conversations, 0, 3) as $conv) {
                echo "  • {$conv['title']} ({$conv['provider_name']} / {$conv['model_name']})\n";
            }
        }
    } catch (Exception $e) {
        echo "❌ Error retrieving user conversations: " . $e->getMessage() . "\n";
    }
    
    // Test 7: Get lab statistics
    echo "\n7. Testing lab statistics...\n";
    
    try {
        $stats = $labManager->getLabStatistics();
        echo "✅ Lab statistics retrieved\n";
        echo "Total conversations: {$stats['total_conversations']}\n";
        echo "Total messages: {$stats['total_messages']}\n";
        echo "Total tokens: {$stats['total_tokens']}\n";
        if (isset($stats['most_used_provider']['name'])) {
            echo "Most used provider: {$stats['most_used_provider']['name']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Error retrieving lab statistics: " . $e->getMessage() . "\n";
    }
    
    // Test 8: Clean up test conversation
    echo "\n8. Cleaning up test conversation...\n";
    
    try {
        $labManager->deleteConversation($conversationId);
        echo "✅ Test conversation deleted successfully\n";
    } catch (Exception $e) {
        echo "⚠️  Warning: Could not delete test conversation: " . $e->getMessage() . "\n";
    }
    
    // Test 9: Test AJAX endpoint
    echo "\n9. Testing AJAX endpoint simulation...\n";
    
    // Simulate the AJAX request data
    $_POST = [
        'ajax' => '1',
        'action' => 'create_conversation',
        'provider_id' => $testData['provider_id'],
        'model_id' => $testData['model_id'],
        'title' => 'AJAX Test Conversation'
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // Capture output
    ob_start();
    
    // Include the AI Lab template to test AJAX handling
    try {
        include 'templates/ai_lab.php';
        $ajaxOutput = ob_get_contents();
        ob_end_clean();
        
        // Try to parse as JSON
        $ajaxResponse = json_decode($ajaxOutput, true);
        
        if ($ajaxResponse && isset($ajaxResponse['success'])) {
            if ($ajaxResponse['success']) {
                echo "✅ AJAX endpoint working: Conversation ID {$ajaxResponse['conversation_id']}\n";
                
                // Clean up the AJAX test conversation
                try {
                    $labManager->deleteConversation($ajaxResponse['conversation_id']);
                    echo "✅ AJAX test conversation cleaned up\n";
                } catch (Exception $e) {
                    echo "⚠️  Warning: Could not delete AJAX test conversation\n";
                }
            } else {
                echo "❌ AJAX endpoint returned error: {$ajaxResponse['error']}\n";
            }
        } else {
            echo "❌ AJAX endpoint returned invalid response: $ajaxOutput\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ AJAX endpoint test failed: " . $e->getMessage() . "\n";
    }
    
    // Reset POST data
    $_POST = [];
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    echo "\n=== TEST SUMMARY ===\n";
    echo "✅ AI Lab Manager: Working\n";
    echo "✅ Conversation Creation: Working\n";
    echo "✅ Conversation Retrieval: Working\n";
    echo "✅ Statistics: Working\n";
    echo "✅ AJAX Endpoint: " . (isset($ajaxResponse['success']) && $ajaxResponse['success'] ? 'Working' : 'Needs Check') . "\n";
    
    echo "\nAI Lab is ready for use!\n";
    echo "Visit: " . BASE_URL . "/?page=ai_lab\n";
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
