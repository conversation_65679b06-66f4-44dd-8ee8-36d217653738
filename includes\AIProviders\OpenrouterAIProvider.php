<?php
/**
 * OpenRouter AI Provider
 *
 * This class implements the OpenRouter AI provider.
 * OpenRouter provides access to multiple AI models through a unified API.
 */
class OpenrouterAIProvider extends AIProvider {
    // Base API URL
    private $apiBaseUrl = 'https://openrouter.ai/api/v1';

    /**
     * Generate text using OpenRouter AI
     *
     * @param string $prompt The prompt to send to the API
     * @param array $options Additional options for the API call
     * @return array Response data including generated text and metadata
     */
    public function generateText($prompt, $options = []) {
        // Get model ID from options or use default
        $modelId = $options['model_id'] ?? null;
        if (!$modelId) {
            throw new Exception("Model ID is required for text generation");
        }

        // Get model data
        $model = $this->db->getRow("SELECT * FROM ai_models WHERE id = ? AND is_active = 1", [$modelId]);
        if (!$model) {
            throw new Exception("Model not found or not active");
        }

        // Prepare messages array for chat completion
        $messages = [];
        
        // Add system prompt if available
        if (!empty($model['system_prompt'])) {
            $messages[] = [
                'role' => 'system',
                'content' => $model['system_prompt']
            ];
        }
        
        // Add user prompt
        $messages[] = [
            'role' => 'user',
            'content' => $prompt
        ];

        // Prepare request data
        $requestData = [
            'model' => $model['model_id'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? $model['max_tokens'] ?? 4096,
            'temperature' => $options['temperature'] ?? $model['temperature'] ?? 0.7,
        ];

        // Add optional parameters
        if (isset($options['top_p'])) {
            $requestData['top_p'] = $options['top_p'];
        }
        if (isset($options['frequency_penalty'])) {
            $requestData['frequency_penalty'] = $options['frequency_penalty'];
        }
        if (isset($options['presence_penalty'])) {
            $requestData['presence_penalty'] = $options['presence_penalty'];
        }

        // Start timer
        $startTime = microtime(true);

        // Make API request
        $response = $this->makeApiRequest('/chat/completions', $requestData);

        // Calculate execution time
        $executionTime = microtime(true) - $startTime;

        // Process response
        $processedResponse = [
            'text' => $response['choices'][0]['message']['content'] ?? '',
            'tokens_used' => $response['usage']['total_tokens'] ?? 0,
            'model' => $response['model'] ?? $model['model_id'],
            'execution_time' => $executionTime,
            'raw_response' => $response
        ];

        // Log API usage if processed post ID is provided
        if (!empty($options['processed_post_id'])) {
            $this->logApiUsage(
                $options['processed_post_id'],
                $options['step_type'] ?? 'content',
                $options['task_type'] ?? 'generate',
                $modelId,
                $prompt,
                json_encode($response),
                $processedResponse['tokens_used'],
                $executionTime
            );
        }

        return $processedResponse;
    }

    /**
     * Generate image using OpenRouter AI
     *
     * @param string $prompt The prompt to send to the API
     * @param array $options Additional options for the API call
     * @return array Response data including image URL and metadata
     */
    public function generateImage($prompt, $options = []) {
        // Get model ID from options or use default
        $modelId = $options['model_id'] ?? null;
        if (!$modelId) {
            throw new Exception("Model ID is required for image generation");
        }

        // Get model data
        $model = $this->db->getRow("SELECT * FROM ai_models WHERE id = ? AND is_active = 1", [$modelId]);
        if (!$model) {
            throw new Exception("Model not found or not active");
        }

        // Prepare request data
        $requestData = [
            'model' => $model['model_id'],
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'size' => $options['size'] ?? '1024x1024',
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // Start timer
        $startTime = microtime(true);

        // Make API request
        $response = $this->makeApiRequest('/images/generations', $requestData);

        // Calculate execution time
        $executionTime = microtime(true) - $startTime;

        // Process response
        $processedResponse = [
            'images' => $response['data'] ?? [],
            'execution_time' => $executionTime,
            'raw_response' => $response
        ];

        // Log API usage if processed post ID is provided
        if (!empty($options['processed_post_id'])) {
            $this->logApiUsage(
                $options['processed_post_id'],
                $options['step_type'] ?? 'image_generation',
                $options['task_type'] ?? 'generate',
                $modelId,
                $prompt,
                json_encode($response),
                0, // No tokens for image generation
                $executionTime
            );
        }

        return $processedResponse;
    }

    /**
     * Test connection and model availability
     *
     * @param string $modelId Model identifier to test
     * @return array Test result
     */
    public function testConnection($modelId = null) {
        try {
            // Use a simple test prompt
            $testPrompt = "Hello, this is a test message. Please respond with 'Test successful'.";
            
            if ($modelId) {
                // Test specific model
                $model = $this->db->getRow("SELECT * FROM ai_models WHERE model_id = ? AND is_active = 1", [$modelId]);
                if (!$model) {
                    throw new Exception("Model not found: $modelId");
                }
                
                $result = $this->generateText($testPrompt, ['model_id' => $model['id']]);
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'model' => $modelId,
                    'response' => $result['text'],
                    'tokens_used' => $result['tokens_used'],
                    'execution_time' => $result['execution_time']
                ];
            } else {
                // Test connection with models endpoint
                $response = $this->makeApiRequest('/models', [], 'GET');
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'models_available' => count($response['data'] ?? [])
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get available models from OpenRouter
     *
     * @return array List of available models
     */
    public function getAvailableModels() {
        try {
            $response = $this->makeApiRequest('/models', [], 'GET');
            return $response['data'] ?? [];
        } catch (Exception $e) {
            throw new Exception("Failed to fetch models: " . $e->getMessage());
        }
    }

    /**
     * Make API request to OpenRouter AI
     *
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param string $method HTTP method
     * @return array Response data
     */
    private function makeApiRequest($endpoint, $data = [], $method = 'POST') {
        $url = $this->apiBaseUrl . $endpoint;

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'HTTP-Referer: ' . (BASE_URL ?? 'http://localhost'), // Required by OpenRouter
            'X-Title: Content Grabber AI' // Optional but recommended
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception('API request failed: ' . curl_error($ch));
        }

        curl_close($ch);

        $responseData = json_decode($response, true);

        if ($httpCode >= 400) {
            $errorMessage = 'Unknown error';
            if (isset($responseData['error'])) {
                if (is_string($responseData['error'])) {
                    $errorMessage = $responseData['error'];
                } elseif (isset($responseData['error']['message'])) {
                    $errorMessage = $responseData['error']['message'];
                }
            }
            throw new Exception('API request failed with code ' . $httpCode . ': ' . $errorMessage);
        }

        return $responseData;
    }
}
?>
