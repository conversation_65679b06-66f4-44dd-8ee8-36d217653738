<?php
// Get providers
$providers = $db->query("SELECT * FROM ai_providers ORDER BY name");

// Handle edit action
if ($action === 'edit' && $id > 0) {
    $provider = $db->getRow("SELECT * FROM ai_providers WHERE id = ?", [$id]);
    if (!$provider) {
        echo '<div class="alert alert-danger">Provider not found.</div>';
        $action = '';
    }
}
?>

<div class="row">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo $action === 'edit' ? 'Edit Provider' : 'Add Provider'; ?></h5>
                <?php if ($action === 'edit'): ?>
                <span class="badge bg-<?php echo $provider['is_active'] ? 'success' : 'danger'; ?>">
                    <?php echo $provider['is_active'] ? 'Active' : 'Inactive'; ?>
                </span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $action === 'edit' ? 'update_provider' : 'add_provider'; ?>">
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-robot text-primary me-1"></i> Provider Name
                        </label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['name']) : ''; ?>" required>
                        <div class="form-text">The name of the AI provider (e.g., NOVITA AI, OpenAI)</div>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link text-primary me-1"></i> Provider Slug
                        </label>
                        <input type="text" class="form-control" id="slug" name="slug" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['slug']) : ''; ?>" <?php echo $action === 'edit' ? 'readonly' : 'required'; ?>>
                        <div class="form-text">A unique identifier for the provider (e.g., novita, openai)</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-info-circle text-primary me-1"></i> Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo $action === 'edit' ? htmlspecialchars($provider['description']) : ''; ?></textarea>
                        <div class="form-text">A brief description of the provider's capabilities</div>
                    </div>

                    <div class="mb-3">
                        <label for="website" class="form-label">
                            <i class="fas fa-globe text-primary me-1"></i> Website
                        </label>
                        <input type="url" class="form-control" id="website" name="website" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['website']) : ''; ?>">
                        <div class="form-text">The provider's website URL</div>
                    </div>

                    <div class="mb-3">
                        <label for="api_base_url" class="form-label">
                            <i class="fas fa-link text-primary me-1"></i> API Base URL
                        </label>
                        <input type="url" class="form-control" id="api_base_url" name="api_base_url" value="<?php echo $action === 'edit' ? htmlspecialchars($provider['api_base_url'] ?? '') : ''; ?>">
                        <div class="form-text">The base URL for API requests (e.g., https://api.openrouter.ai/api/v1)</div>
                    </div>

                    <div class="mb-3">
                        <label for="auth_type" class="form-label">
                            <i class="fas fa-key text-primary me-1"></i> Authentication Type
                        </label>
                        <select class="form-select" id="auth_type" name="auth_type">
                            <option value="bearer" <?php echo ($action === 'edit' && ($provider['auth_type'] ?? 'bearer') === 'bearer') ? 'selected' : ''; ?>>Bearer Token</option>
                            <option value="api_key" <?php echo ($action === 'edit' && ($provider['auth_type'] ?? '') === 'api_key') ? 'selected' : ''; ?>>API Key Header</option>
                            <option value="custom" <?php echo ($action === 'edit' && ($provider['auth_type'] ?? '') === 'custom') ? 'selected' : ''; ?>>Custom Headers</option>
                        </select>
                        <div class="form-text">How the API key should be sent to the provider</div>
                    </div>

                    <div class="mb-3">
                        <label for="rate_limit_per_minute" class="form-label">
                            <i class="fas fa-tachometer-alt text-primary me-1"></i> Rate Limit (per minute)
                        </label>
                        <input type="number" class="form-control" id="rate_limit_per_minute" name="rate_limit_per_minute" value="<?php echo $action === 'edit' ? ($provider['rate_limit_per_minute'] ?? '') : ''; ?>" min="1">
                        <div class="form-text">Maximum requests per minute (optional)</div>
                    </div>

                    <?php if ($action === 'edit'): ?>
                    <div class="mb-3 form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="supports_streaming" name="supports_streaming" value="1" <?php echo ($provider['supports_streaming'] ?? 0) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="supports_streaming">Supports Streaming</label>
                        <div class="form-text">Whether this provider supports streaming responses</div>
                    </div>
                    <?php endif; ?>

                    <?php if ($action === 'edit'): ?>
                    <div class="mb-3 form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" <?php echo $provider['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active</label>
                        <div class="form-text">Inactive providers will not be available for use</div>
                    </div>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> <?php echo $action === 'edit' ? 'Update' : 'Add'; ?> Provider
                        </button>

                        <?php if ($action === 'edit'): ?>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteProviderModal">
                            <i class="fas fa-trash-alt me-1"></i> Delete
                        </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">AI Providers</h5>

                <?php if ($action === 'edit'): ?>
                <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=providers" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-plus me-1"></i> Add New
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body p-0">
                <?php if (empty($providers)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-0">No AI providers found. Add your first provider to start using AI features.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th>Provider</th>
                                <th>Description</th>
                                <th>Models</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($providers as $p): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-box bg-primary bg-opacity-10 text-primary rounded p-2 me-2">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($p['name']); ?></strong>
                                            <div class="small text-muted"><?php echo htmlspecialchars($p['slug']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($p['description'] ?? ''); ?></td>
                                <td>
                                    <?php
                                    $modelCount = $db->getValue("SELECT COUNT(*) FROM ai_models WHERE provider_id = ?", [$p['id']]);
                                    echo $modelCount;
                                    ?>
                                </td>
                                <td>
                                    <?php if ($p['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo BASE_URL; ?>/?page=ai_settings&section=providers&action=edit&id=<?php echo $p['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="testProvider(<?php echo $p['id']; ?>, '<?php echo htmlspecialchars($p['name']); ?>')">
                                            <i class="fas fa-vial"></i> Test
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($action === 'edit'): ?>
<!-- Delete Provider Modal -->
<div class="modal fade" id="deleteProviderModal" tabindex="-1" aria-labelledby="deleteProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProviderModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the provider "<?php echo htmlspecialchars($provider['name']); ?>"?</p>
                <p class="text-danger">This action cannot be undone. All API keys and models associated with this provider will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post">
                    <input type="hidden" name="action" value="delete_provider">
                    <input type="hidden" name="provider_id" value="<?php echo $provider['id']; ?>">
                    <button type="submit" class="btn btn-danger">Delete Provider</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Test Provider Modal -->
<div class="modal fade" id="testProviderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test AI Provider</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-provider-content">
                    <form id="test-provider-form">
                        <input type="hidden" id="test-provider-id">

                        <div class="mb-3">
                            <label for="test-api-key" class="form-label">API Key</label>
                            <input type="password" class="form-control" id="test-api-key" placeholder="Enter API key to test" required>
                            <div class="form-text">This key will only be used for testing and will not be saved</div>
                        </div>

                        <div class="mb-3">
                            <label for="test-prompt" class="form-label">Test Prompt</label>
                            <textarea class="form-control" id="test-prompt" rows="3" placeholder="Enter a test prompt...">Hello! This is a test message. Please respond with a brief greeting.</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="test-model-select" class="form-label">Model (Optional)</label>
                            <select class="form-select" id="test-model-select">
                                <option value="">Test connection only</option>
                            </select>
                            <div class="form-text">Select a model to test specific model functionality</div>
                        </div>
                    </form>

                    <div id="test-results" class="mt-4" style="display: none;">
                        <h6>Test Results:</h6>
                        <div id="test-results-content"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="runProviderTest()">
                    <i class="fas fa-play me-1"></i> Run Test
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentTestProviderId = null;

function testProvider(providerId, providerName) {
    currentTestProviderId = providerId;
    document.getElementById('test-provider-id').value = providerId;
    document.querySelector('#testProviderModal .modal-title').textContent = 'Test ' + providerName;

    // Load models for this provider
    loadProviderModels(providerId);

    // Reset form and results
    document.getElementById('test-provider-form').reset();
    document.getElementById('test-results').style.display = 'none';
    document.getElementById('test-prompt').value = 'Hello! This is a test message. Please respond with a brief greeting.';

    // Show modal
    new bootstrap.Modal(document.getElementById('testProviderModal')).show();
}

function loadProviderModels(providerId) {
    const modelSelect = document.getElementById('test-model-select');
    modelSelect.innerHTML = '<option value="">Loading models...</option>';

    fetch('<?php echo BASE_URL; ?>/api/test_provider.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_models',
            provider_id: providerId
        })
    })
    .then(response => response.json())
    .then(data => {
        modelSelect.innerHTML = '<option value="">Test connection only</option>';

        if (data.success && data.models) {
            data.models.forEach(model => {
                if (model.is_active) {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name + ' (' + model.model_id + ')';
                    modelSelect.appendChild(option);
                }
            });
        }
    })
    .catch(error => {
        console.error('Error loading models:', error);
        modelSelect.innerHTML = '<option value="">Error loading models</option>';
    });
}

function runProviderTest() {
    const providerId = document.getElementById('test-provider-id').value;
    const apiKey = document.getElementById('test-api-key').value;
    const prompt = document.getElementById('test-prompt').value;
    const modelId = document.getElementById('test-model-select').value;

    if (!apiKey) {
        alert('Please enter an API key');
        return;
    }

    // Show loading
    const runButton = document.querySelector('#testProviderModal .btn-primary');
    const originalText = runButton.innerHTML;
    runButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Testing...';
    runButton.disabled = true;

    // Hide previous results
    document.getElementById('test-results').style.display = 'none';

    // Determine test action
    const action = modelId ? 'test_model' : 'test_connection';
    const requestData = {
        action: action,
        provider_id: parseInt(providerId),
        api_key: apiKey
    };

    if (modelId) {
        requestData.model_id = parseInt(modelId);
        requestData.prompt = prompt;
    }

    fetch('<?php echo BASE_URL; ?>/api/test_provider.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        displayTestResults(data);
    })
    .catch(error => {
        console.error('Error testing provider:', error);
        displayTestResults({
            success: false,
            error: 'Network error: ' + error.message
        });
    })
    .finally(() => {
        runButton.innerHTML = originalText;
        runButton.disabled = false;
    });
}

function displayTestResults(data) {
    const resultsDiv = document.getElementById('test-results-content');

    if (data.success) {
        let html = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>Test successful!</div>';

        if (data.response) {
            html += '<div class="card"><div class="card-header"><strong>AI Response:</strong></div>';
            html += '<div class="card-body">' + escapeHtml(data.response) + '</div></div>';
        }

        if (data.tokens_used || data.execution_time) {
            html += '<div class="row mt-3">';
            if (data.tokens_used) {
                html += '<div class="col-md-6"><small class="text-muted"><i class="fas fa-coins me-1"></i>Tokens: ' + data.tokens_used + '</small></div>';
            }
            if (data.execution_time) {
                html += '<div class="col-md-6"><small class="text-muted"><i class="fas fa-clock me-1"></i>Time: ' + data.execution_time.toFixed(2) + 's</small></div>';
            }
            html += '</div>';
        }

        if (data.model_used) {
            html += '<div class="mt-2"><small class="text-muted"><i class="fas fa-robot me-1"></i>Model: ' + escapeHtml(data.model_used) + '</small></div>';
        }

        resultsDiv.innerHTML = html;
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Test failed: ' + escapeHtml(data.error || 'Unknown error') + '</div>';
    }

    document.getElementById('test-results').style.display = 'block';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
