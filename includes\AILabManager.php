<?php
/**
 * AI Lab Manager
 * 
 * This class manages AI testing conversations and interactions in the LAB environment.
 */
class AILabManager {
    private $db;
    private $userId;
    
    /**
     * Constructor
     * 
     * @param Database $db Database instance
     * @param int $userId Current user ID
     */
    public function __construct(Database $db, $userId) {
        $this->db = $db;
        $this->userId = $userId;
    }
    
    /**
     * Create a new conversation
     * 
     * @param int $providerId Provider ID
     * @param int $modelId Model ID
     * @param string $title Optional conversation title
     * @return int Conversation ID
     */
    public function createConversation($providerId, $modelId, $title = null) {
        // Validate provider and model
        $provider = $this->db->getRow("SELECT * FROM ai_providers WHERE id = ? AND is_active = 1", [$providerId]);
        if (!$provider) {
            throw new Exception("Provider not found or not active");
        }
        
        $model = $this->db->getRow("SELECT * FROM ai_models WHERE id = ? AND provider_id = ? AND is_active = 1", [$modelId, $providerId]);
        if (!$model) {
            throw new Exception("Model not found or not active for this provider");
        }
        
        // Generate title if not provided
        if (!$title) {
            $title = "Lab Test - " . $provider['name'] . " / " . $model['name'] . " - " . date('Y-m-d H:i');
        }
        
        // Create conversation
        return $this->db->insert('ai_lab_conversations', [
            'user_id' => $this->userId,
            'title' => $title,
            'provider_id' => $providerId,
            'model_id' => $modelId,
            'is_active' => 1
        ]);
    }
    
    /**
     * Get user's conversations
     * 
     * @param int $limit Number of conversations to retrieve
     * @param int $offset Offset for pagination
     * @return array List of conversations
     */
    public function getUserConversations($limit = 20, $offset = 0) {
        return $this->db->query("
            SELECT c.*, p.name as provider_name, p.slug as provider_slug, 
                   m.name as model_name, m.model_id as model_identifier,
                   (SELECT COUNT(*) FROM ai_lab_messages WHERE conversation_id = c.id) as message_count,
                   (SELECT created_at FROM ai_lab_messages WHERE conversation_id = c.id ORDER BY created_at DESC LIMIT 1) as last_message_at
            FROM ai_lab_conversations c
            JOIN ai_providers p ON c.provider_id = p.id
            JOIN ai_models m ON c.model_id = m.id
            WHERE c.user_id = ? AND c.is_active = 1
            ORDER BY c.updated_at DESC
            LIMIT ? OFFSET ?
        ", [$this->userId, $limit, $offset]);
    }
    
    /**
     * Get conversation by ID
     * 
     * @param int $conversationId Conversation ID
     * @return array|null Conversation data
     */
    public function getConversation($conversationId) {
        $conversation = $this->db->getRow("
            SELECT c.*, p.name as provider_name, p.slug as provider_slug, 
                   m.name as model_name, m.model_id as model_identifier
            FROM ai_lab_conversations c
            JOIN ai_providers p ON c.provider_id = p.id
            JOIN ai_models m ON c.model_id = m.id
            WHERE c.id = ? AND c.user_id = ? AND c.is_active = 1
        ", [$conversationId, $this->userId]);
        
        return $conversation;
    }
    
    /**
     * Get conversation messages
     * 
     * @param int $conversationId Conversation ID
     * @param int $limit Number of messages to retrieve
     * @param int $offset Offset for pagination
     * @return array List of messages
     */
    public function getConversationMessages($conversationId, $limit = 50, $offset = 0) {
        // Verify conversation belongs to user
        $conversation = $this->getConversation($conversationId);
        if (!$conversation) {
            throw new Exception("Conversation not found or access denied");
        }
        
        return $this->db->query("
            SELECT * FROM ai_lab_messages 
            WHERE conversation_id = ? 
            ORDER BY created_at ASC 
            LIMIT ? OFFSET ?
        ", [$conversationId, $limit, $offset]);
    }
    
    /**
     * Send a message and get AI response
     * 
     * @param int $conversationId Conversation ID
     * @param string $userMessage User's message
     * @param array $options Additional options for the AI call
     * @return array Response data
     */
    public function sendMessage($conversationId, $userMessage, $options = []) {
        // Get conversation
        $conversation = $this->getConversation($conversationId);
        if (!$conversation) {
            throw new Exception("Conversation not found or access denied");
        }
        
        // Save user message
        $userMessageId = $this->db->insert('ai_lab_messages', [
            'conversation_id' => $conversationId,
            'role' => 'user',
            'content' => $userMessage
        ]);
        
        try {
            // Get AI provider
            require_once 'AIProvider.php';
            $provider = AIProvider::create($this->db, $conversation['provider_slug']);
            
            // Prepare conversation context
            $messages = $this->getConversationMessages($conversationId);
            $contextPrompt = $this->buildContextPrompt($messages, $userMessage);
            
            // Generate AI response
            $startTime = microtime(true);
            $response = $provider->generateText($contextPrompt, array_merge($options, [
                'model_id' => $conversation['model_id']
            ]));
            $executionTime = microtime(true) - $startTime;
            
            // Save AI response
            $assistantMessageId = $this->db->insert('ai_lab_messages', [
                'conversation_id' => $conversationId,
                'role' => 'assistant',
                'content' => $response['text'],
                'tokens_used' => $response['tokens_used'] ?? null,
                'execution_time' => $executionTime,
                'model_used' => $response['model'] ?? $conversation['model_identifier']
            ]);
            
            // Update conversation timestamp
            $this->db->update('ai_lab_conversations', 
                ['updated_at' => date('Y-m-d H:i:s')], 
                'id = ?', 
                [$conversationId]
            );
            
            return [
                'success' => true,
                'user_message_id' => $userMessageId,
                'assistant_message_id' => $assistantMessageId,
                'response' => $response['text'],
                'tokens_used' => $response['tokens_used'] ?? 0,
                'execution_time' => $executionTime,
                'model_used' => $response['model'] ?? $conversation['model_identifier']
            ];
            
        } catch (Exception $e) {
            // Save error message
            $this->db->insert('ai_lab_messages', [
                'conversation_id' => $conversationId,
                'role' => 'assistant',
                'content' => 'Error: ' . $e->getMessage(),
                'error_message' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'user_message_id' => $userMessageId
            ];
        }
    }
    
    /**
     * Build context prompt from conversation history
     * 
     * @param array $messages Previous messages
     * @param string $newMessage New user message
     * @return string Context prompt
     */
    private function buildContextPrompt($messages, $newMessage) {
        $context = "";
        
        // Add previous messages (limit to last 10 to avoid token limits)
        $recentMessages = array_slice($messages, -10);
        
        foreach ($recentMessages as $message) {
            if ($message['role'] === 'user') {
                $context .= "User: " . $message['content'] . "\n";
            } elseif ($message['role'] === 'assistant' && empty($message['error_message'])) {
                $context .= "Assistant: " . $message['content'] . "\n";
            }
        }
        
        // Add new message
        $context .= "User: " . $newMessage . "\n";
        $context .= "Assistant: ";
        
        return $context;
    }
    
    /**
     * Delete a conversation
     * 
     * @param int $conversationId Conversation ID
     * @return bool Success status
     */
    public function deleteConversation($conversationId) {
        // Verify conversation belongs to user
        $conversation = $this->getConversation($conversationId);
        if (!$conversation) {
            throw new Exception("Conversation not found or access denied");
        }
        
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Delete messages
            $this->db->delete('ai_lab_messages', 'conversation_id = ?', [$conversationId]);
            
            // Delete conversation
            $this->db->delete('ai_lab_conversations', 'id = ?', [$conversationId]);
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Update conversation title
     * 
     * @param int $conversationId Conversation ID
     * @param string $title New title
     * @return bool Success status
     */
    public function updateConversationTitle($conversationId, $title) {
        // Verify conversation belongs to user
        $conversation = $this->getConversation($conversationId);
        if (!$conversation) {
            throw new Exception("Conversation not found or access denied");
        }
        
        return $this->db->update('ai_lab_conversations', 
            ['title' => $title], 
            'id = ?', 
            [$conversationId]
        );
    }
    
    /**
     * Get lab statistics
     * 
     * @return array Statistics data
     */
    public function getLabStatistics() {
        $stats = [];
        
        // Total conversations
        $stats['total_conversations'] = $this->db->getValue("
            SELECT COUNT(*) FROM ai_lab_conversations WHERE user_id = ? AND is_active = 1
        ", [$this->userId]);
        
        // Total messages
        $stats['total_messages'] = $this->db->getValue("
            SELECT COUNT(*) FROM ai_lab_messages m
            JOIN ai_lab_conversations c ON m.conversation_id = c.id
            WHERE c.user_id = ? AND c.is_active = 1
        ", [$this->userId]);
        
        // Total tokens used
        $stats['total_tokens'] = $this->db->getValue("
            SELECT SUM(tokens_used) FROM ai_lab_messages m
            JOIN ai_lab_conversations c ON m.conversation_id = c.id
            WHERE c.user_id = ? AND c.is_active = 1 AND m.tokens_used IS NOT NULL
        ", [$this->userId]) ?? 0;
        
        // Most used provider
        $stats['most_used_provider'] = $this->db->getRow("
            SELECT p.name, COUNT(*) as usage_count
            FROM ai_lab_conversations c
            JOIN ai_providers p ON c.provider_id = p.id
            WHERE c.user_id = ? AND c.is_active = 1
            GROUP BY p.id, p.name
            ORDER BY usage_count DESC
            LIMIT 1
        ", [$this->userId]);
        
        return $stats;
    }
}
?>
