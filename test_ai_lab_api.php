<?php
/**
 * Test AI Lab API
 * 
 * This script tests the AI Lab API endpoint.
 */

// Load configuration
require_once 'config.php';

// Start session
session_start();

// Ensure user session exists
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use admin user for testing
}

header('Content-Type: text/plain');

echo "=== TESTING AI LAB API ===\n\n";

// Test 1: Test connection
echo "1. Testing API connection...\n";

$testData = [
    'action' => 'test_connection'
];

$response = makeApiRequest($testData);
echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";

if (!$response || !$response['success']) {
    echo "❌ API connection failed\n";
    exit;
}

echo "✅ API connection successful\n\n";

// Test 2: Get statistics
echo "2. Testing get statistics...\n";

$testData = [
    'action' => 'get_statistics'
];

$response = makeApiRequest($testData);
echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";

// Test 3: Get conversations
echo "3. Testing get conversations...\n";

$testData = [
    'action' => 'get_conversations'
];

$response = makeApiRequest($testData);
echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";

// Test 4: Test conversation creation
echo "4. Testing conversation creation...\n";

// First, get available providers and models
try {
    $pdo = get_db_connection();
    
    $stmt = $pdo->prepare("
        SELECT p.id as provider_id, p.name as provider_name, m.id as model_id, m.name as model_name
        FROM ai_providers p
        JOIN ai_models m ON p.id = m.provider_id
        WHERE p.is_active = 1 AND m.is_active = 1
        ORDER BY p.id, m.id
        LIMIT 1
    ");
    $stmt->execute();
    $testModel = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testModel) {
        echo "Using test provider: {$testModel['provider_name']} (ID: {$testModel['provider_id']})\n";
        echo "Using test model: {$testModel['model_name']} (ID: {$testModel['model_id']})\n";
        
        $testData = [
            'action' => 'create_conversation',
            'provider_id' => $testModel['provider_id'],
            'model_id' => $testModel['model_id'],
            'title' => 'API Test Conversation - ' . date('Y-m-d H:i:s')
        ];
        
        $response = makeApiRequest($testData);
        echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
        
        if ($response && $response['success']) {
            echo "✅ Conversation created successfully with ID: {$response['conversation_id']}\n";
            
            // Test 5: Get the created conversation
            echo "\n5. Testing get conversation...\n";
            
            $testData = [
                'action' => 'get_conversation',
                'conversation_id' => $response['conversation_id']
            ];
            
            $getResponse = makeApiRequest($testData);
            echo "Response: " . json_encode($getResponse, JSON_PRETTY_PRINT) . "\n\n";
            
            // Test 6: Clean up - delete the test conversation
            echo "6. Cleaning up test conversation...\n";
            
            $testData = [
                'action' => 'delete_conversation',
                'conversation_id' => $response['conversation_id']
            ];
            
            $deleteResponse = makeApiRequest($testData);
            echo "Response: " . json_encode($deleteResponse, JSON_PRETTY_PRINT) . "\n\n";
            
            if ($deleteResponse && $deleteResponse['success']) {
                echo "✅ Test conversation deleted successfully\n";
            }
        } else {
            echo "❌ Failed to create conversation\n";
        }
    } else {
        echo "❌ No valid provider-model combination found for testing\n";
        echo "Please ensure you have active providers and models in the database\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during conversation creation test: " . $e->getMessage() . "\n";
}

echo "\n=== API TEST COMPLETE ===\n";

/**
 * Make API request to AI Lab endpoint
 */
function makeApiRequest($data) {
    $url = BASE_URL . '/api/ai_lab.php';
    
    // Initialize cURL
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, sys_get_temp_dir() . '/ai_lab_test_cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, sys_get_temp_dir() . '/ai_lab_test_cookies.txt');
    
    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Raw Response: $response\n";
    
    // Try to decode JSON
    $decoded = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "JSON Decode Error: " . json_last_error_msg() . "\n";
        return null;
    }
    
    return $decoded;
}
?>
