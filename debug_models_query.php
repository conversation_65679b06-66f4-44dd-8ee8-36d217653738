<?php
/**
 * Debug Models Query
 * 
 * This script specifically debugs the JOIN query used in the models template.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

header('Content-Type: text/plain');

echo "=== DEBUGGING MODELS QUERY ===\n\n";

try {
    // 1. Check table existence
    echo "1. Checking table existence...\n";
    $providersTableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");
    
    echo "ai_providers table exists: " . (!empty($providersTableExists) ? "YES" : "NO") . "\n";
    echo "ai_models table exists: " . (!empty($modelsTableExists) ? "YES" : "NO") . "\n\n";
    
    if (empty($providersTableExists) || empty($modelsTableExists)) {
        echo "ERROR: Required tables missing!\n";
        exit;
    }
    
    // 2. Raw data check
    echo "2. Raw data in tables...\n";
    
    $providersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers");
    $modelsCount = $db->getValue("SELECT COUNT(*) FROM ai_models");
    
    echo "Total providers: $providersCount\n";
    echo "Total models: $modelsCount\n\n";
    
    // 3. Show all providers
    echo "3. All providers:\n";
    $allProviders = $db->query("SELECT id, name, slug, is_active FROM ai_providers ORDER BY id");
    foreach ($allProviders as $p) {
        $status = $p['is_active'] ? 'ACTIVE' : 'INACTIVE';
        echo "   ID: {$p['id']}, Name: {$p['name']}, Slug: {$p['slug']}, Status: $status\n";
    }
    echo "\n";
    
    // 4. Show all models
    echo "4. All models:\n";
    $allModels = $db->query("SELECT id, provider_id, name, model_id, is_active FROM ai_models ORDER BY id");
    foreach ($allModels as $m) {
        $status = $m['is_active'] ? 'ACTIVE' : 'INACTIVE';
        echo "   ID: {$m['id']}, Provider ID: {$m['provider_id']}, Name: {$m['name']}, Model ID: {$m['model_id']}, Status: $status\n";
    }
    echo "\n";
    
    // 5. Test the exact query from the template
    echo "5. Testing the exact JOIN query from template...\n";
    $templateQuery = "
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        ORDER BY p.name, m.name
    ";
    
    echo "Query: $templateQuery\n";
    
    $templateResults = $db->query($templateQuery);
    echo "Results count: " . count($templateResults) . "\n";
    
    if (empty($templateResults)) {
        echo "ERROR: JOIN query returns no results!\n\n";
        
        // 6. Debug why JOIN fails
        echo "6. Debugging JOIN failure...\n";
        
        // Check for models with invalid provider_id
        $invalidProviderModels = $db->query("
            SELECT m.id, m.name, m.provider_id 
            FROM ai_models m 
            WHERE m.provider_id NOT IN (SELECT id FROM ai_providers)
        ");
        
        if (!empty($invalidProviderModels)) {
            echo "Models with invalid provider_id:\n";
            foreach ($invalidProviderModels as $m) {
                echo "   Model ID: {$m['id']}, Name: {$m['name']}, Invalid Provider ID: {$m['provider_id']}\n";
            }
        } else {
            echo "No models with invalid provider_id found.\n";
        }
        
        // Check for models with inactive providers
        $inactiveProviderModels = $db->query("
            SELECT m.id, m.name, m.provider_id, p.name as provider_name, p.is_active
            FROM ai_models m
            JOIN ai_providers p ON m.provider_id = p.id
            WHERE p.is_active = 0
        ");
        
        if (!empty($inactiveProviderModels)) {
            echo "Models with inactive providers:\n";
            foreach ($inactiveProviderModels as $m) {
                echo "   Model: {$m['name']}, Provider: {$m['provider_name']} (INACTIVE)\n";
            }
        } else {
            echo "No models with inactive providers found.\n";
        }
        
        // Test with LEFT JOIN to see all models
        echo "\n7. Testing with LEFT JOIN to see all models...\n";
        $leftJoinResults = $db->query("
            SELECT m.*, p.name as provider_name, p.is_active as provider_active
            FROM ai_models m
            LEFT JOIN ai_providers p ON m.provider_id = p.id
            ORDER BY m.id
        ");
        
        echo "LEFT JOIN results count: " . count($leftJoinResults) . "\n";
        foreach ($leftJoinResults as $m) {
            $providerInfo = $m['provider_name'] ? "{$m['provider_name']} (Active: {$m['provider_active']})" : "NO PROVIDER";
            echo "   Model: {$m['name']}, Provider: $providerInfo\n";
        }
        
    } else {
        echo "SUCCESS: JOIN query works!\n";
        foreach ($templateResults as $m) {
            echo "   {$m['provider_name']} / {$m['name']}\n";
        }
    }
    
    // 8. Test the template logic
    echo "\n8. Testing template logic...\n";
    
    // Simulate the template logic
    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
    if (!empty($tableExists)) {
        echo "ai_providers table check: PASSED\n";
        
        $providers = $db->query("SELECT * FROM ai_providers ORDER BY name");
        echo "Providers query result count: " . count($providers) . "\n";
        
        $modelsTableExists = $db->query("SHOW TABLES LIKE 'ai_models'");
        if (!empty($modelsTableExists)) {
            echo "ai_models table check: PASSED\n";
            
            $models = $db->query("
                SELECT m.*, p.name as provider_name
                FROM ai_models m
                JOIN ai_providers p ON m.provider_id = p.id
                ORDER BY p.name, m.name
            ");
            echo "Models query result count: " . count($models) . "\n";
            
            if (empty($models)) {
                echo "TEMPLATE RESULT: 'No models found' message will be shown\n";
            } else {
                echo "TEMPLATE RESULT: Models table will be shown\n";
            }
        } else {
            echo "ai_models table check: FAILED\n";
        }
    } else {
        echo "ai_providers table check: FAILED\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
?>
