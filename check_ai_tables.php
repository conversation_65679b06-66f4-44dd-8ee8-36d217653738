<?php
/**
 * Check AI Tables Status
 * 
 * This script checks if all AI-related tables exist and shows their status.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Include tool header
$pageTitle = 'AI Tables Status Check';
$currentPage = 'check_ai_tables';
include_once 'includes/tool_header.php';

echo '<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">AI Database Tables Status</h5>
    </div>
    <div class="card-body">
        <pre>';

echo "Checking AI database tables status...\n\n";

// List of required AI tables
$requiredTables = [
    'ai_providers' => 'AI Providers',
    'ai_api_keys' => 'API Keys',
    'ai_models' => 'AI Models',
    'ai_workflows' => 'Workflows',
    'ai_workflow_steps' => 'Workflow Steps',
    'ai_processed_posts' => 'Processed Posts',
    'ai_processing_logs' => 'Processing Logs',
    'ai_lab_conversations' => 'Lab Conversations',
    'ai_lab_messages' => 'Lab Messages'
];

$allTablesExist = true;
$tableStatus = [];

foreach ($requiredTables as $tableName => $description) {
    try {
        $tableExists = $db->query("SHOW TABLES LIKE '$tableName'");
        if (!empty($tableExists)) {
            // Table exists, get row count
            $rowCount = $db->getValue("SELECT COUNT(*) FROM $tableName");
            $tableStatus[$tableName] = [
                'exists' => true,
                'rows' => $rowCount,
                'description' => $description
            ];
            echo "✅ $description ($tableName): EXISTS ($rowCount rows)\n";
        } else {
            $tableStatus[$tableName] = [
                'exists' => false,
                'rows' => 0,
                'description' => $description
            ];
            echo "❌ $description ($tableName): MISSING\n";
            $allTablesExist = false;
        }
    } catch (Exception $e) {
        $tableStatus[$tableName] = [
            'exists' => false,
            'rows' => 0,
            'description' => $description,
            'error' => $e->getMessage()
        ];
        echo "❌ $description ($tableName): ERROR - " . $e->getMessage() . "\n";
        $allTablesExist = false;
    }
}

echo "\n" . str_repeat("=", 50) . "\n";

if ($allTablesExist) {
    echo "🎉 ALL AI TABLES ARE PRESENT!\n\n";
    
    // Show summary
    echo "Summary:\n";
    foreach ($tableStatus as $tableName => $status) {
        if ($status['exists']) {
            echo "  • {$status['description']}: {$status['rows']} records\n";
        }
    }
    
    // Check for providers
    $providersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    echo "\nActive Providers: $providersCount\n";
    
    if ($providersCount > 0) {
        $providers = $db->query("SELECT name, slug FROM ai_providers WHERE is_active = 1");
        foreach ($providers as $provider) {
            echo "  • {$provider['name']} ({$provider['slug']})\n";
        }
    }
    
    // Check for models
    $modelsCount = $db->getValue("SELECT COUNT(*) FROM ai_models WHERE is_active = 1");
    echo "\nActive Models: $modelsCount\n";
    
    // Check for API keys
    $apiKeysCount = $db->getValue("SELECT COUNT(*) FROM ai_api_keys WHERE is_active = 1");
    echo "Active API Keys: $apiKeysCount\n";
    
} else {
    echo "⚠️  SOME AI TABLES ARE MISSING!\n\n";
    echo "Missing tables:\n";
    foreach ($tableStatus as $tableName => $status) {
        if (!$status['exists']) {
            echo "  • {$status['description']} ($tableName)\n";
        }
    }
    echo "\nPlease run the database update script to create missing tables.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

// Check for required columns in ai_providers
if (isset($tableStatus['ai_providers']) && $tableStatus['ai_providers']['exists']) {
    echo "\nChecking ai_providers table structure...\n";
    
    $requiredColumns = [
        'api_base_url' => 'API Base URL',
        'auth_type' => 'Authentication Type',
        'custom_headers' => 'Custom Headers',
        'rate_limit_per_minute' => 'Rate Limit',
        'supports_streaming' => 'Streaming Support'
    ];
    
    $providerColumns = $db->query("SHOW COLUMNS FROM ai_providers");
    $existingColumns = array_column($providerColumns, 'Field');
    
    $missingColumns = [];
    foreach ($requiredColumns as $column => $description) {
        if (in_array($column, $existingColumns)) {
            echo "✅ $description ($column): EXISTS\n";
        } else {
            echo "❌ $description ($column): MISSING\n";
            $missingColumns[] = $column;
        }
    }
    
    if (empty($missingColumns)) {
        echo "\n🎉 All required columns exist in ai_providers table!\n";
    } else {
        echo "\n⚠️  Missing columns in ai_providers table. Please run the update script.\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Status check completed.\n";

echo '</pre>
    </div>
    <div class="card-footer bg-white">
        <div class="row">
            <div class="col-md-6">
                <a href="' . BASE_URL . '/update_ai_tables.php" class="btn btn-primary">
                    <i class="fas fa-database me-1"></i> Update AI Tables
                </a>
            </div>
            <div class="col-md-6 text-end">
                <a href="' . BASE_URL . '/setup_default_providers.php" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i> Setup Default Providers
                </a>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-6">
                <a href="' . BASE_URL . '/?page=ai_settings" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-1"></i> AI Settings
                </a>
            </div>
            <div class="col-md-6 text-end">
                <a href="' . BASE_URL . '/?page=ai_lab" class="btn btn-outline-success">
                    <i class="fas fa-flask me-1"></i> AI Lab
                </a>
            </div>
        </div>
    </div>
</div>';

// Include tool footer
include_once 'includes/tool_footer.php';
?>
