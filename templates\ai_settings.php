<?php
// Set page title
$pageTitle = 'AI Settings';

// Load required files
require_once 'includes/AIWorkflowManager.php';

// Initialize workflow manager
$workflowManager = new AIWorkflowManager($db);

// Get action
$action = $_GET['action'] ?? '';
$section = $_GET['section'] ?? 'providers';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $formAction = $_POST['action'] ?? '';

    switch ($formAction) {
        case 'add_provider':
            // Add new provider
            $name = $_POST['name'];
            $slug = $_POST['slug'];
            $description = $_POST['description'] ?? null;
            $website = $_POST['website'] ?? null;
            $apiBaseUrl = $_POST['api_base_url'] ?? null;
            $authType = $_POST['auth_type'] ?? 'bearer';
            $rateLimitPerMinute = !empty($_POST['rate_limit_per_minute']) ? (int)$_POST['rate_limit_per_minute'] : null;
            $supportsStreaming = isset($_POST['supports_streaming']) ? 1 : 0;

            // Validate inputs
            if (empty($name) || empty($slug)) {
                $errorMessage = 'Provider name and slug are required.';
            } else {
                try {
                    // Check if slug already exists
                    $existingProvider = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE slug = ?", [$slug]);
                    if ($existingProvider > 0) {
                        $errorMessage = 'A provider with this slug already exists.';
                    } else {
                        // Insert provider
                        $db->insert('ai_providers', [
                            'name' => $name,
                            'slug' => $slug,
                            'description' => $description,
                            'website' => $website,
                            'api_base_url' => $apiBaseUrl,
                            'auth_type' => $authType,
                            'rate_limit_per_minute' => $rateLimitPerMinute,
                            'supports_streaming' => $supportsStreaming,
                            'is_active' => 1
                        ]);

                        $successMessage = 'Provider added successfully!';
                    }
                } catch (Exception $e) {
                    $errorMessage = 'Error adding provider: ' . $e->getMessage();
                }
            }
            break;

        case 'update_provider':
            // Update provider
            $providerId = (int)$_POST['provider_id'];
            $name = $_POST['name'];
            $description = $_POST['description'] ?? null;
            $website = $_POST['website'] ?? null;
            $apiBaseUrl = $_POST['api_base_url'] ?? null;
            $authType = $_POST['auth_type'] ?? 'bearer';
            $rateLimitPerMinute = !empty($_POST['rate_limit_per_minute']) ? (int)$_POST['rate_limit_per_minute'] : null;
            $supportsStreaming = isset($_POST['supports_streaming']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            // Validate inputs
            if (empty($providerId) || empty($name)) {
                $errorMessage = 'Provider ID and name are required.';
            } else {
                try {
                    // Update provider
                    $db->update('ai_providers', [
                        'name' => $name,
                        'description' => $description,
                        'website' => $website,
                        'api_base_url' => $apiBaseUrl,
                        'auth_type' => $authType,
                        'rate_limit_per_minute' => $rateLimitPerMinute,
                        'supports_streaming' => $supportsStreaming,
                        'is_active' => $isActive
                    ], 'id = ?', [$providerId]);

                    $successMessage = 'Provider updated successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error updating provider: ' . $e->getMessage();
                }
            }
            break;

        case 'delete_provider':
            // Delete provider
            $providerId = (int)$_POST['provider_id'];

            try {
                // Start transaction
                $db->beginTransaction();

                // Delete API keys for this provider
                $db->delete('ai_api_keys', 'provider_id = ?', [$providerId]);

                // Get models for this provider
                $models = $db->query("SELECT id FROM ai_models WHERE provider_id = ?", [$providerId]);

                // Delete workflow steps for these models
                foreach ($models as $model) {
                    $db->delete('ai_workflow_steps', 'model_id = ?', [$model['id']]);
                }

                // Delete models for this provider
                $db->delete('ai_models', 'provider_id = ?', [$providerId]);

                // Delete provider
                $db->delete('ai_providers', 'id = ?', [$providerId]);

                // Commit transaction
                $db->commit();

                $successMessage = 'Provider deleted successfully!';

                // Redirect to providers list
                header('Location: ' . BASE_URL . '/?page=ai_settings&section=providers');
                exit;
            } catch (Exception $e) {
                // Rollback transaction
                $db->rollback();

                $errorMessage = 'Error deleting provider: ' . $e->getMessage();
            }
            break;

        case 'add_api_key':
            // Add new API key
            $providerId = (int)$_POST['provider_id'];
            $name = $_POST['name'];
            $apiKey = $_POST['api_key'];
            $isDefault = isset($_POST['is_default']) ? 1 : 0;

            // Validate inputs
            if (empty($providerId) || empty($name) || empty($apiKey)) {
                $errorMessage = 'All fields are required.';
            } else {
                try {
                    // If this key is set as default, unset any existing default for this provider
                    if ($isDefault) {
                        $db->query("UPDATE ai_api_keys SET is_default = 0 WHERE provider_id = ?", [$providerId]);
                    }

                    // Insert API key
                    $apiKeyId = $db->insert('ai_api_keys', [
                        'provider_id' => $providerId,
                        'name' => $name,
                        'api_key' => $apiKey,
                        'is_default' => $isDefault,
                        'is_active' => 1,
                        'usage_count' => 0
                    ]);

                    // Set session variable for success message
                    $_SESSION['api_key_added'] = true;

                    $successMessage = 'API key added successfully with ID: ' . $apiKeyId;

                    // Redirect to API keys page to show the new key
                    header('Location: ' . BASE_URL . '/?page=ai_settings&section=api_keys');
                    exit;
                } catch (Exception $e) {
                    $errorMessage = 'Error adding API key: ' . $e->getMessage();
                }
            }
            break;

        case 'update_api_key':
            // Update API key
            $keyId = (int)$_POST['key_id'];
            $name = $_POST['name'];
            $apiKey = $_POST['api_key'];
            $isDefault = isset($_POST['is_default']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            // Validate inputs
            if (empty($keyId) || empty($name)) {
                $errorMessage = 'Key ID and name are required.';
            } else {
                try {
                    // Get provider ID for this key
                    $providerId = $db->getValue("SELECT provider_id FROM ai_api_keys WHERE id = ?", [$keyId]);

                    // If this key is set as default, unset any existing default for this provider
                    if ($isDefault) {
                        $db->query("UPDATE ai_api_keys SET is_default = 0 WHERE provider_id = ? AND id != ?", [$providerId, $keyId]);
                    }

                    // Update data
                    $data = [
                        'name' => $name,
                        'is_default' => $isDefault,
                        'is_active' => $isActive
                    ];

                    // Only update API key if provided
                    if (!empty($apiKey)) {
                        $data['api_key'] = $apiKey;
                    }

                    // Update API key
                    $db->update('ai_api_keys', $data, 'id = ?', [$keyId]);

                    $successMessage = 'API key updated successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error updating API key: ' . $e->getMessage();
                }
            }
            break;

        case 'delete_api_key':
            // Delete API key
            $keyId = (int)$_POST['key_id'];

            try {
                // Delete API key
                $db->delete('ai_api_keys', 'id = ?', [$keyId]);

                $successMessage = 'API key deleted successfully!';
            } catch (Exception $e) {
                $errorMessage = 'Error deleting API key: ' . $e->getMessage();
            }
            break;

        case 'add_model':
            // Add new model
            $providerId = (int)$_POST['provider_id'];
            $name = $_POST['name'];
            $modelIdentifier = $_POST['model_identifier'];
            $type = $_POST['type'];
            $systemPrompt = $_POST['system_prompt'] ?? null;
            $maxTokens = !empty($_POST['max_tokens']) ? (int)$_POST['max_tokens'] : null;
            $temperature = !empty($_POST['temperature']) ? (float)$_POST['temperature'] : 0.7;
            $capabilities = isset($_POST['capabilities']) ? $_POST['capabilities'] : [];

            // Validate inputs
            if (empty($providerId) || empty($name) || empty($modelIdentifier) || empty($type)) {
                $errorMessage = 'Provider, name, model identifier, and type are required.';
            } else {
                try {
                    // Insert model
                    $db->insert('ai_models', [
                        'provider_id' => $providerId,
                        'name' => $name,
                        'model_id' => $modelIdentifier,
                        'type' => $type,
                        'capabilities' => json_encode($capabilities),
                        'system_prompt' => $systemPrompt,
                        'max_tokens' => $maxTokens,
                        'temperature' => $temperature,
                        'is_active' => 1
                    ]);

                    $successMessage = 'Model added successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error adding model: ' . $e->getMessage();
                }
            }
            break;

        case 'update_model':
            // Update model
            $modelId = (int)$_POST['model_id'];
            $name = $_POST['name'];
            $modelIdentifier = $_POST['model_identifier'];
            $type = $_POST['type'];
            $systemPrompt = $_POST['system_prompt'] ?? null;
            $maxTokens = !empty($_POST['max_tokens']) ? (int)$_POST['max_tokens'] : null;
            $temperature = !empty($_POST['temperature']) ? (float)$_POST['temperature'] : 0.7;
            $capabilities = isset($_POST['capabilities']) ? $_POST['capabilities'] : [];
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            // Validate inputs
            if (empty($modelId) || empty($name) || empty($modelIdentifier) || empty($type)) {
                $errorMessage = 'Model ID, name, model identifier, and type are required.';
            } else {
                try {
                    // Update model
                    $db->update('ai_models', [
                        'name' => $name,
                        'model_id' => $modelIdentifier,
                        'type' => $type,
                        'capabilities' => json_encode($capabilities),
                        'system_prompt' => $systemPrompt,
                        'max_tokens' => $maxTokens,
                        'temperature' => $temperature,
                        'is_active' => $isActive
                    ], 'id = ?', [$modelId]);

                    $successMessage = 'Model updated successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error updating model: ' . $e->getMessage();
                }
            }
            break;

        case 'delete_model':
            // Delete model
            $modelId = (int)$_POST['model_id'];

            try {
                // Check if model is used in any workflow steps
                $usedInWorkflow = $db->getValue("SELECT COUNT(*) FROM ai_workflow_steps WHERE model_id = ?", [$modelId]);
                if ($usedInWorkflow > 0) {
                    $errorMessage = 'Cannot delete model because it is used in one or more workflows.';
                } else {
                    // Delete model
                    $db->delete('ai_models', 'id = ?', [$modelId]);

                    $successMessage = 'Model deleted successfully!';
                }
            } catch (Exception $e) {
                $errorMessage = 'Error deleting model: ' . $e->getMessage();
            }
            break;

        case 'add_workflow':
            // Add new workflow
            $name = $_POST['name'];
            $description = $_POST['description'] ?? null;
            $isDefault = isset($_POST['is_default']) ? 1 : 0;

            // Validate inputs
            if (empty($name)) {
                $errorMessage = 'Workflow name is required.';
            } else {
                try {
                    // Create workflow
                    $workflowId = $workflowManager->createWorkflow([
                        'name' => $name,
                        'description' => $description,
                        'is_default' => $isDefault
                    ]);

                    $successMessage = 'Workflow added successfully!';

                    // Redirect to workflow steps page
                    header('Location: ' . BASE_URL . '/?page=ai_settings&section=workflows&action=edit&id=' . $workflowId);
                    exit;
                } catch (Exception $e) {
                    $errorMessage = 'Error adding workflow: ' . $e->getMessage();
                }
            }
            break;

        case 'update_workflow':
            // Update workflow
            $workflowId = (int)$_POST['workflow_id'];
            $name = $_POST['name'];
            $description = $_POST['description'] ?? null;
            $isDefault = isset($_POST['is_default']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            // Validate inputs
            if (empty($workflowId) || empty($name)) {
                $errorMessage = 'Workflow ID and name are required.';
            } else {
                try {
                    // Update workflow
                    $workflowManager->updateWorkflow($workflowId, [
                        'name' => $name,
                        'description' => $description,
                        'is_default' => $isDefault,
                        'is_active' => $isActive
                    ]);

                    $successMessage = 'Workflow updated successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error updating workflow: ' . $e->getMessage();
                }
            }
            break;

        case 'delete_workflow':
            // Delete workflow
            $workflowId = (int)$_POST['workflow_id'];

            try {
                // Delete workflow
                $workflowManager->deleteWorkflow($workflowId);

                $successMessage = 'Workflow deleted successfully!';

                // Redirect to workflows list
                header('Location: ' . BASE_URL . '/?page=ai_settings&section=workflows');
                exit;
            } catch (Exception $e) {
                $errorMessage = 'Error deleting workflow: ' . $e->getMessage();
            }
            break;

        case 'add_workflow_step':
            // Add workflow step
            $workflowId = (int)$_POST['workflow_id'];
            $modelId = (int)$_POST['model_id'];
            $stepType = $_POST['step_type'];
            $taskType = $_POST['task_type'];
            $promptTemplate = $_POST['prompt_template'] ?? null;

            // Validate inputs
            if (empty($workflowId) || empty($modelId) || empty($stepType) || empty($taskType)) {
                $errorMessage = 'Workflow ID, model, step type, and task type are required.';
            } else {
                try {
                    // Add workflow step
                    $workflowManager->addWorkflowStep($workflowId, [
                        'model_id' => $modelId,
                        'step_type' => $stepType,
                        'task_type' => $taskType,
                        'prompt_template' => $promptTemplate
                    ]);

                    $successMessage = 'Workflow step added successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error adding workflow step: ' . $e->getMessage();
                }
            }
            break;

        case 'update_workflow_step':
            // Update workflow step
            $stepId = (int)$_POST['step_id'];
            $modelId = (int)$_POST['model_id'];
            $taskType = $_POST['task_type'];
            $promptTemplate = $_POST['prompt_template'] ?? null;
            $isActive = isset($_POST['is_active']) ? 1 : 0;

            // Validate inputs
            if (empty($stepId) || empty($modelId) || empty($taskType)) {
                $errorMessage = 'Step ID, model, and task type are required.';
            } else {
                try {
                    // Update workflow step
                    $workflowManager->updateWorkflowStep($stepId, [
                        'model_id' => $modelId,
                        'task_type' => $taskType,
                        'prompt_template' => $promptTemplate,
                        'is_active' => $isActive
                    ]);

                    $successMessage = 'Workflow step updated successfully!';
                } catch (Exception $e) {
                    $errorMessage = 'Error updating workflow step: ' . $e->getMessage();
                }
            }
            break;

        case 'delete_workflow_step':
            // Delete workflow step
            $stepId = (int)$_POST['step_id'];
            $workflowId = (int)$_POST['workflow_id'];

            try {
                // Delete workflow step
                $workflowManager->deleteWorkflowStep($stepId);

                $successMessage = 'Workflow step deleted successfully!';
            } catch (Exception $e) {
                $errorMessage = 'Error deleting workflow step: ' . $e->getMessage();
            }
            break;
    }
}

// Display success/error messages
if (isset($successMessage)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($successMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($errorMessage)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($errorMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">AI Settings</h1>

    <div>
        <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-outline-primary">
            <i class="fas fa-database me-1"></i> Update AI Database
        </a>
    </div>
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="d-flex">
            <div class="me-3">
                <i class="fas fa-info-circle text-primary fa-2x"></i>
            </div>
            <div>
                <p class="mb-0">
                    Configure AI providers, models, and workflows to enhance your content with AI processing capabilities.
                    Start by adding API keys for your AI providers, then configure models and create workflows to process your content.
                </p>
                <?php
                // Check if ai_providers table exists and if there are any providers
                try {
                    $tableExists = $db->query("SHOW TABLES LIKE 'ai_providers'");
                    $providersCount = !empty($tableExists) ? $db->getValue("SELECT COUNT(*) FROM ai_providers") : 0;
                    if ($providersCount == 0):
                ?>

                <div class="mt-2">
                    <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-sync-alt me-1"></i> Initialize AI Database
                    </a>
                    <span class="ms-2 text-danger">No AI providers found. Click the button to initialize the AI database.</span>
                </div>
                <?php
                    endif;
                } catch (Exception $e) {
                    // Table doesn't exist, show initialization button
                ?>
                <div class="mt-2">
                    <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-sync-alt me-1"></i> Initialize AI Database
                    </a>
                    <span class="ms-2 text-danger">AI database tables not found. Click the button to initialize the AI database.</span>
                </div>
                <?php
                }
                ?>
            </div>
        </div>
    </div>
</div>

<ul class="nav nav-tabs nav-fill mb-4">
    <li class="nav-item">
        <a class="nav-link <?php echo $section === 'providers' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_settings&section=providers">
            <i class="fas fa-robot me-1"></i> Providers
            <div class="small text-muted">Manage AI providers</div>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo $section === 'api_keys' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_settings&section=api_keys">
            <i class="fas fa-key me-1"></i> API Keys
            <div class="small text-muted">Configure provider credentials</div>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo $section === 'models' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_settings&section=models">
            <i class="fas fa-brain me-1"></i> Models
            <div class="small text-muted">Set up AI models</div>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo $section === 'workflows' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/?page=ai_settings&section=workflows">
            <i class="fas fa-project-diagram me-1"></i> Workflows
            <div class="small text-muted">Create processing workflows</div>
        </a>
    </li>
</ul>

<?php
// Include the appropriate section template
$sectionFile = 'templates/ai_settings/' . $section . '.php';
if (file_exists($sectionFile)) {
    include $sectionFile;
} else {
    echo '<div class="alert alert-warning">Section not found.</div>';
}
?>
