<?php
// Set page title
$pageTitle = 'AI Settings';

// Load required files and initialize workflow manager
$workflowManager = null;
$workflows = [];

try {
    if (file_exists('includes/AIWorkflowManager.php')) {
        require_once 'includes/AIWorkflowManager.php';
        $workflowManager = new AIWorkflowManager($db);
    }
} catch (Exception $e) {
    // Workflow manager not available yet
    error_log('AIWorkflowManager error: ' . $e->getMessage());
}

// Get action
$action = $_GET['action'] ?? '';
$section = $_GET['section'] ?? 'providers';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle AJAX requests for modals
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');

    $formAction = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];

    try {
        switch ($formAction) {
            case 'add_provider':
                $name = $_POST['name'];
                $slug = $_POST['slug'];
                $description = $_POST['description'] ?? null;
                $website = $_POST['website'] ?? null;
                $apiBaseUrl = $_POST['api_base_url'] ?? null;
                $authType = $_POST['auth_type'] ?? 'bearer';
                $rateLimitPerMinute = !empty($_POST['rate_limit_per_minute']) ? (int)$_POST['rate_limit_per_minute'] : null;
                $supportsStreaming = isset($_POST['supports_streaming']) ? 1 : 0;

                if (empty($name) || empty($slug)) {
                    throw new Exception('Provider name and slug are required.');
                }

                // Check if slug already exists
                $existingProvider = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE slug = ?", [$slug]);
                if ($existingProvider > 0) {
                    throw new Exception('A provider with this slug already exists.');
                }

                // Insert provider
                $providerId = $db->insert('ai_providers', [
                    'name' => $name,
                    'slug' => $slug,
                    'description' => $description,
                    'website' => $website,
                    'api_base_url' => $apiBaseUrl,
                    'auth_type' => $authType,
                    'rate_limit_per_minute' => $rateLimitPerMinute,
                    'supports_streaming' => $supportsStreaming,
                    'is_active' => 1
                ]);

                $response['success'] = true;
                $response['message'] = 'Provider added successfully!';
                $response['provider_id'] = $providerId;
                break;

            case 'delete_provider':
                $providerId = (int)$_POST['provider_id'];

                // Start transaction
                $db->beginTransaction();

                // Check if provider has API keys or models
                $apiKeyCount = $db->getValue("SELECT COUNT(*) FROM ai_api_keys WHERE provider_id = ?", [$providerId]);
                $modelCount = $db->getValue("SELECT COUNT(*) FROM ai_models WHERE provider_id = ?", [$providerId]);

                if ($apiKeyCount > 0 || $modelCount > 0) {
                    // Smart delete: remove related data
                    $db->delete('ai_api_keys', 'provider_id = ?', [$providerId]);

                    $models = $db->query("SELECT id FROM ai_models WHERE provider_id = ?", [$providerId]);
                    foreach ($models as $model) {
                        $db->delete('ai_workflow_steps', 'model_id = ?', [$model['id']]);
                    }
                    $db->delete('ai_models', 'provider_id = ?', [$providerId]);
                }

                // Delete provider
                $db->delete('ai_providers', 'id = ?', [$providerId]);
                $db->commit();

                $response['success'] = true;
                $response['message'] = 'Provider and all related data deleted successfully!';
                break;

            case 'add_api_key':
                $providerId = (int)$_POST['provider_id'];
                $name = $_POST['name'];
                $apiKey = $_POST['api_key'];
                $isDefault = isset($_POST['is_default']) ? 1 : 0;

                if (empty($providerId) || empty($name) || empty($apiKey)) {
                    throw new Exception('All fields are required.');
                }

                // If this key is set as default, unset any existing default for this provider
                if ($isDefault) {
                    $db->query("UPDATE ai_api_keys SET is_default = 0 WHERE provider_id = ?", [$providerId]);
                }

                // Insert API key
                $apiKeyId = $db->insert('ai_api_keys', [
                    'provider_id' => $providerId,
                    'key_name' => $name,
                    'api_key' => $apiKey,
                    'is_default' => $isDefault,
                    'is_active' => 1,
                    'usage_count' => 0
                ]);

                $response['success'] = true;
                $response['message'] = 'API key added successfully!';
                $response['api_key_id'] = $apiKeyId;
                break;

            case 'delete_api_key':
                $keyId = (int)$_POST['key_id'];

                // Check if this is the only key for the provider
                $providerId = $db->getValue("SELECT provider_id FROM ai_api_keys WHERE id = ?", [$keyId]);
                $keyCount = $db->getValue("SELECT COUNT(*) FROM ai_api_keys WHERE provider_id = ?", [$providerId]);

                if ($keyCount <= 1) {
                    throw new Exception('Cannot delete the last API key for this provider.');
                }

                $db->delete('ai_api_keys', 'id = ?', [$keyId]);

                $response['success'] = true;
                $response['message'] = 'API key deleted successfully!';
                break;

            case 'add_model':
                $providerId = (int)$_POST['provider_id'];
                $name = $_POST['name'];
                $modelIdentifier = $_POST['model_identifier'];
                $type = $_POST['type'];
                $systemPrompt = $_POST['system_prompt'] ?? null;
                $maxTokens = !empty($_POST['max_tokens']) ? (int)$_POST['max_tokens'] : null;
                $temperature = !empty($_POST['temperature']) ? (float)$_POST['temperature'] : 0.7;
                $capabilities = isset($_POST['capabilities']) ? $_POST['capabilities'] : [];

                if (empty($providerId) || empty($name) || empty($modelIdentifier) || empty($type)) {
                    throw new Exception('Provider, name, model identifier, and type are required.');
                }

                // Insert model
                $modelId = $db->insert('ai_models', [
                    'provider_id' => $providerId,
                    'name' => $name,
                    'model_id' => $modelIdentifier,
                    'type' => $type,
                    'capabilities' => json_encode($capabilities),
                    'system_prompt' => $systemPrompt,
                    'max_tokens' => $maxTokens,
                    'temperature' => $temperature,
                    'is_active' => 1
                ]);

                $response['success'] = true;
                $response['message'] = 'Model added successfully!';
                $response['model_id'] = $modelId;
                break;

            case 'delete_model':
                $modelId = (int)$_POST['model_id'];

                // Check if model is used in any workflow steps
                $usedInWorkflow = $db->getValue("SELECT COUNT(*) FROM ai_workflow_steps WHERE model_id = ?", [$modelId]);
                if ($usedInWorkflow > 0) {
                    throw new Exception('Cannot delete model because it is used in one or more workflows.');
                }

                $db->delete('ai_models', 'id = ?', [$modelId]);

                $response['success'] = true;
                $response['message'] = 'Model deleted successfully!';
                break;

            // Edit actions
            case 'edit_provider':
                $id = intval($_POST['provider_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $slug = trim($_POST['slug'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $api_base_url = trim($_POST['api_base_url'] ?? '');
                $auth_type = trim($_POST['auth_type'] ?? 'bearer');
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                if ($id > 0 && !empty($name) && !empty($slug)) {
                    $result = $db->update('ai_providers', [
                        'name' => $name,
                        'slug' => $slug,
                        'description' => $description,
                        'api_base_url' => $api_base_url,
                        'auth_type' => $auth_type,
                        'is_active' => $is_active,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'id = ?', [$id]);

                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'Provider updated successfully!';
                    } else {
                        throw new Exception('Failed to update provider');
                    }
                } else {
                    throw new Exception('Please fill in all required fields');
                }
                break;

            case 'edit_api_key':
                $id = intval($_POST['api_key_id'] ?? 0);
                $provider_id = intval($_POST['provider_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $api_key = trim($_POST['api_key'] ?? '');
                $is_default = isset($_POST['is_default']) ? 1 : 0;
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                if ($id > 0 && $provider_id > 0 && !empty($name) && !empty($api_key)) {
                    // If setting as default, unset other defaults for this provider
                    if ($is_default) {
                        $db->update('ai_api_keys', ['is_default' => 0], 'provider_id = ? AND id != ?', [$provider_id, $id]);
                    }

                    $result = $db->update('ai_api_keys', [
                        'provider_id' => $provider_id,
                        'name' => $name,
                        'api_key' => $api_key,
                        'is_default' => $is_default,
                        'is_active' => $is_active,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'id = ?', [$id]);

                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'API key updated successfully!';
                    } else {
                        throw new Exception('Failed to update API key');
                    }
                } else {
                    throw new Exception('Please fill in all required fields');
                }
                break;

            case 'edit_model':
                $id = intval($_POST['model_id_hidden'] ?? 0);
                $provider_id = intval($_POST['provider_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $model_id = trim($_POST['model_id'] ?? '');
                $type = trim($_POST['type'] ?? '');
                $capabilities = isset($_POST['capabilities']) ? implode(',', $_POST['capabilities']) : '';
                $max_tokens = intval($_POST['max_tokens'] ?? 0) ?: null;
                $temperature = floatval($_POST['temperature'] ?? 0) ?: null;
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                if ($id > 0 && $provider_id > 0 && !empty($name) && !empty($model_id) && !empty($type)) {
                    $result = $db->update('ai_models', [
                        'provider_id' => $provider_id,
                        'name' => $name,
                        'model_id' => $model_id,
                        'type' => $type,
                        'capabilities' => $capabilities,
                        'max_tokens' => $max_tokens,
                        'temperature' => $temperature,
                        'is_active' => $is_active,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'id = ?', [$id]);

                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'Model updated successfully!';
                    } else {
                        throw new Exception('Failed to update model');
                    }
                } else {
                    throw new Exception('Please fill in all required fields');
                }
                break;

            // Get data for editing
            case 'get_provider':
                $id = intval($_GET['id'] ?? 0);
                if ($id > 0) {
                    $pdo = get_db_connection();
                    $stmt = $pdo->prepare("SELECT * FROM ai_providers WHERE id = ?");
                    $stmt->execute([$id]);
                    $provider = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($provider) {
                        $response['success'] = true;
                        $response['provider'] = $provider;
                    } else {
                        throw new Exception('Provider not found');
                    }
                } else {
                    throw new Exception('Invalid provider ID');
                }
                break;

            case 'get_api_key':
                $id = intval($_GET['id'] ?? 0);
                if ($id > 0) {
                    $pdo = get_db_connection();
                    $stmt = $pdo->prepare("SELECT * FROM ai_api_keys WHERE id = ?");
                    $stmt->execute([$id]);
                    $apiKey = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($apiKey) {
                        $response['success'] = true;
                        $response['api_key'] = $apiKey;
                    } else {
                        throw new Exception('API key not found');
                    }
                } else {
                    throw new Exception('Invalid API key ID');
                }
                break;

            case 'get_model':
                $id = intval($_GET['id'] ?? 0);
                if ($id > 0) {
                    $pdo = get_db_connection();
                    $stmt = $pdo->prepare("SELECT * FROM ai_models WHERE id = ?");
                    $stmt->execute([$id]);
                    $model = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($model) {
                        $response['success'] = true;
                        $response['model'] = $model;
                    } else {
                        throw new Exception('Model not found');
                    }
                } else {
                    throw new Exception('Invalid model ID');
                }
                break;

            default:
                throw new Exception('Invalid action: ' . $formAction);
        }
    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }

    echo json_encode($response);
    exit;
}

// Get data for display using direct PDO to bypass Database class issues
try {
    $pdo = get_db_connection();

    // Get providers
    $stmt = $pdo->prepare("SELECT * FROM ai_providers ORDER BY name");
    $stmt->execute();
    $providers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (!$providers) $providers = [];

    // Get API keys with provider names
    $stmt = $pdo->prepare("
        SELECT ak.*, p.name as provider_name
        FROM ai_api_keys ak
        JOIN ai_providers p ON ak.provider_id = p.id
        ORDER BY p.name, ak.name
    ");
    $stmt->execute();
    $apiKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (!$apiKeys) $apiKeys = [];

    // Get models with provider names
    $stmt = $pdo->prepare("
        SELECT m.*, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        ORDER BY p.name, m.name
    ");
    $stmt->execute();
    $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (!$models) $models = [];

} catch (Exception $e) {
    error_log('Error fetching AI settings data: ' . $e->getMessage());
    $providers = [];
    $apiKeys = [];
    $models = [];
}

// Data loading successful - debug removed
// Get workflows if workflow manager is available
if ($workflowManager) {
    try {
        $workflows = $workflowManager->getWorkflows(false); // Get all workflows, not just active ones
    } catch (Exception $e) {
        error_log('Error loading workflows: ' . $e->getMessage());
        $workflows = [];
    }
}
?>

<!-- AI Settings Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="fas fa-brain text-primary me-2"></i>AI Settings
        </h1>
        <p class="text-muted mb-0">Configure AI providers, models, and workflows</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
        <a href="<?php echo BASE_URL; ?>/update_ai_tables.php" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-database me-1"></i> Update Database
        </a>
    </div>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs mb-4" id="aiSettingsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo $section === 'providers' ? 'active' : ''; ?>"
                id="providers-tab" data-bs-toggle="tab" data-bs-target="#providers"
                type="button" role="tab">
            <i class="fas fa-server me-1"></i>
            Providers
            <span class="badge bg-primary ms-2"><?php echo count($providers); ?></span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo $section === 'api_keys' ? 'active' : ''; ?>"
                id="api-keys-tab" data-bs-toggle="tab" data-bs-target="#api-keys"
                type="button" role="tab">
            <i class="fas fa-key me-1"></i>
            API Keys
            <span class="badge bg-success ms-2"><?php echo count($apiKeys); ?></span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo $section === 'models' ? 'active' : ''; ?>"
                id="models-tab" data-bs-toggle="tab" data-bs-target="#models"
                type="button" role="tab">
            <i class="fas fa-robot me-1"></i>
            Models
            <span class="badge bg-info ms-2"><?php echo count($models); ?></span>
        </button>
    </li>
    <?php if ($workflowManager): ?>
    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo $section === 'workflows' ? 'active' : ''; ?>"
                id="workflows-tab" data-bs-toggle="tab" data-bs-target="#workflows"
                type="button" role="tab">
            <i class="fas fa-project-diagram me-1"></i>
            Workflows
            <span class="badge bg-warning ms-2"><?php echo count($workflows); ?></span>
        </button>
    </li>
    <?php endif; ?>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="aiSettingsTabContent">
    <!-- Providers Tab -->
    <div class="tab-pane fade <?php echo $section === 'providers' ? 'show active' : ''; ?>"
         id="providers" role="tabpanel">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">AI Providers</h5>
            <button type="button" class="btn btn-primary btn-sm" onclick="showAddProviderModal()">
                <i class="fas fa-plus me-1"></i> Add Provider
            </button>
        </div>

        <div class="row" id="providers-list">
            <?php foreach ($providers as $provider): ?>
            <div class="col-md-6 col-lg-4 mb-3" data-provider-id="<?php echo $provider['id']; ?>">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-server text-primary me-2"></i>
                            <strong><?php echo htmlspecialchars($provider['name']); ?></strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                    type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="editProvider(<?php echo $provider['id']; ?>)">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#"
                                       onclick="deleteProvider(<?php echo $provider['id']; ?>, '<?php echo htmlspecialchars($provider['name']); ?>')">
                                        <i class="fas fa-trash me-1"></i> Delete
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-2"><?php echo htmlspecialchars($provider['description'] ?? 'No description'); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-<?php echo $provider['is_active'] ? 'success' : 'secondary'; ?>">
                                <?php echo $provider['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                            <small class="text-muted"><?php echo htmlspecialchars($provider['slug']); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- API Keys Tab -->
    <div class="tab-pane fade <?php echo $section === 'api_keys' ? 'show active' : ''; ?>"
         id="api-keys" role="tabpanel">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">API Keys</h5>
            <button type="button" class="btn btn-success btn-sm" onclick="showAddApiKeyModal()">
                <i class="fas fa-plus me-1"></i> Add API Key
            </button>
        </div>

        <div class="row" id="api-keys-list">
            <?php foreach ($apiKeys as $apiKey): ?>
            <div class="col-md-6 col-lg-4 mb-3" data-api-key-id="<?php echo $apiKey['id']; ?>">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-key text-success me-2"></i>
                            <strong><?php echo htmlspecialchars($apiKey['name']); ?></strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                    type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="editApiKey(<?php echo $apiKey['id']; ?>)">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#"
                                       onclick="deleteApiKey(<?php echo $apiKey['id']; ?>, '<?php echo htmlspecialchars($apiKey['name']); ?>')">
                                        <i class="fas fa-trash me-1"></i> Delete
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-2">Provider: <?php echo htmlspecialchars($apiKey['provider_name']); ?></p>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-<?php echo $apiKey['is_active'] ? 'success' : 'secondary'; ?>">
                                <?php echo $apiKey['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                            <?php if ($apiKey['is_default']): ?>
                            <span class="badge bg-primary">Default</span>
                            <?php endif; ?>
                        </div>
                        <small class="text-muted">
                            Used: <?php echo number_format($apiKey['usage_count']); ?> times
                        </small>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Models Tab -->
    <div class="tab-pane fade <?php echo $section === 'models' ? 'show active' : ''; ?>"
         id="models" role="tabpanel">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">AI Models</h5>
            <button type="button" class="btn btn-info btn-sm" onclick="showAddModelModal()">
                <i class="fas fa-plus me-1"></i> Add Model
            </button>
        </div>

        <div class="row" id="models-list">
            <?php foreach ($models as $model): ?>
            <div class="col-md-6 col-lg-4 mb-3" data-model-id="<?php echo $model['id']; ?>">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-robot text-info me-2"></i>
                            <strong><?php echo htmlspecialchars($model['name']); ?></strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                    type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="editModel(<?php echo $model['id']; ?>)">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#"
                                       onclick="deleteModel(<?php echo $model['id']; ?>, '<?php echo htmlspecialchars($model['name']); ?>')">
                                        <i class="fas fa-trash me-1"></i> Delete
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-2">Provider: <?php echo htmlspecialchars($model['provider_name']); ?></p>
                        <p class="text-muted small mb-2">Model ID: <?php echo htmlspecialchars($model['model_id']); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-<?php echo $model['is_active'] ? 'success' : 'secondary'; ?>">
                                <?php echo $model['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                            <span class="badge bg-secondary"><?php echo ucfirst($model['type']); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Workflows Tab -->
    <?php if ($workflowManager): ?>
    <div class="tab-pane fade <?php echo $section === 'workflows' ? 'show active' : ''; ?>"
         id="workflows" role="tabpanel">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">AI Workflows</h5>
            <button type="button" class="btn btn-warning btn-sm" onclick="showAddWorkflowModal()">
                <i class="fas fa-plus me-1"></i> Add Workflow
            </button>
        </div>

        <div class="row" id="workflows-list">
            <?php if (!empty($workflows)): ?>
                <?php foreach ($workflows as $workflow): ?>
                <div class="col-md-6 col-lg-4 mb-3" data-workflow-id="<?php echo $workflow['id']; ?>">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-project-diagram text-warning me-2"></i>
                                <strong><?php echo htmlspecialchars($workflow['name']); ?></strong>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                        type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="editWorkflow(<?php echo $workflow['id']; ?>)">
                                            <i class="fas fa-edit me-1"></i> Edit
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#"
                                           onclick="deleteWorkflow(<?php echo $workflow['id']; ?>, '<?php echo htmlspecialchars($workflow['name']); ?>')">
                                            <i class="fas fa-trash me-1"></i> Delete
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small mb-2"><?php echo htmlspecialchars($workflow['description'] ?? 'No description'); ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-<?php echo $workflow['is_active'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $workflow['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                                <?php if ($workflow['is_default']): ?>
                                <span class="badge bg-primary">Default</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Workflows Found</h5>
                        <p class="text-muted">Create your first AI workflow to get started.</p>
                        <button type="button" class="btn btn-warning" onclick="showAddWorkflowModal()">
                            <i class="fas fa-plus me-1"></i> Add Your First Workflow
                        </button>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Add Provider Modal -->
<div class="modal fade" id="addProviderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>Add AI Provider
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addProviderForm">
                <div class="modal-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="provider_name" class="form-label fw-bold">
                                <i class="fas fa-tag text-primary me-1"></i>Provider Name *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="provider_name" name="name" required>
                            <div class="form-text">Display name for the AI provider</div>
                        </div>
                        <div class="col-md-6">
                            <label for="provider_slug" class="form-label fw-bold">
                                <i class="fas fa-link text-primary me-1"></i>Slug *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="provider_slug" name="slug" required>
                            <div class="form-text">URL-friendly identifier (auto-generated)</div>
                        </div>
                        <div class="col-12">
                            <label for="provider_description" class="form-label fw-bold">
                                <i class="fas fa-align-left text-primary me-1"></i>Description
                            </label>
                            <textarea class="form-control border-2" id="provider_description" name="description" rows="3" placeholder="Brief description of the AI provider..."></textarea>
                            <div class="form-text">Brief description of the AI provider</div>
                        </div>
                        <div class="col-md-6">
                            <label for="provider_website" class="form-label fw-bold">
                                <i class="fas fa-globe text-primary me-1"></i>Website
                            </label>
                            <input type="url" class="form-control form-control-lg border-2" id="provider_website" name="website" placeholder="https://example.com">
                            <div class="form-text">Provider's official website</div>
                        </div>
                        <div class="col-md-6">
                            <label for="provider_api_base_url" class="form-label fw-bold">
                                <i class="fas fa-server text-primary me-1"></i>API Base URL
                            </label>
                            <input type="url" class="form-control form-control-lg border-2" id="provider_api_base_url" name="api_base_url" placeholder="https://api.example.com/v1">
                            <div class="form-text">Base URL for API requests</div>
                        </div>
                        <div class="col-md-6">
                            <label for="provider_auth_type" class="form-label fw-bold">
                                <i class="fas fa-shield-alt text-primary me-1"></i>Authentication Type
                            </label>
                            <select class="form-select form-select-lg border-2" id="provider_auth_type" name="auth_type">
                                <option value="bearer">Bearer Token</option>
                                <option value="api_key">API Key</option>
                                <option value="oauth">OAuth</option>
                                <option value="basic">Basic Auth</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="provider_rate_limit" class="form-label fw-bold">
                                <i class="fas fa-tachometer-alt text-primary me-1"></i>Rate Limit (per minute)
                            </label>
                            <input type="number" class="form-control form-control-lg border-2" id="provider_rate_limit" name="rate_limit_per_minute" min="1" placeholder="60">
                            <div class="form-text">Maximum requests per minute</div>
                        </div>
                        <div class="col-12">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="provider_supports_streaming" name="supports_streaming" value="1">
                                        <label class="form-check-label fw-bold" for="provider_supports_streaming">
                                            <i class="fas fa-stream text-info me-1"></i>Supports Streaming
                                        </label>
                                        <div class="form-text">Provider supports real-time streaming responses</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="provider_is_active" name="is_active" value="1" checked>
                                        <label class="form-check-label fw-bold" for="provider_is_active">
                                            <i class="fas fa-toggle-on text-success me-1"></i>Active Provider
                                        </label>
                                        <div class="form-text">Enable this provider for use</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light border-0">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-1"></i>Add Provider
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add API Key Modal -->
<div class="modal fade" id="addApiKeyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-success text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>Add API Key
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addApiKeyForm">
                <div class="modal-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="api_key_provider" class="form-label fw-bold">
                                <i class="fas fa-robot text-success me-1"></i>AI Provider *
                            </label>
                            <select class="form-select form-select-lg border-2" id="api_key_provider" name="provider_id" required>
                                <option value="">Select Provider...</option>
                                <?php foreach ($providers as $provider): ?>
                                <option value="<?php echo $provider['id']; ?>">
                                    <?php echo htmlspecialchars($provider['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Choose the AI provider for this key</div>
                        </div>
                        <div class="col-md-6">
                            <label for="api_key_name" class="form-label fw-bold">
                                <i class="fas fa-tag text-success me-1"></i>Key Name *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="api_key_name" name="name" required placeholder="My API Key">
                            <div class="form-text">Friendly name for this API key</div>
                        </div>
                        <div class="col-12">
                            <label for="api_key_value" class="form-label fw-bold">
                                <i class="fas fa-key text-success me-1"></i>API Key *
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg border-2" id="api_key_value" name="api_key" required placeholder="sk-...">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('api_key_value', this)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Your API key from the provider (will be encrypted when stored)</div>
                        </div>
                        <div class="col-12">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="api_key_is_default" name="is_default" value="1">
                                        <label class="form-check-label fw-bold" for="api_key_is_default">
                                            <i class="fas fa-star text-warning me-1"></i>Default Key
                                        </label>
                                        <div class="form-text">Use as default for this provider</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="api_key_is_active" name="is_active" value="1" checked>
                                        <label class="form-check-label fw-bold" for="api_key_is_active">
                                            <i class="fas fa-toggle-on text-success me-1"></i>Active Key
                                        </label>
                                        <div class="form-text">Enable this API key</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light border-0">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-plus me-1"></i>Add API Key
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Model Modal -->
<div class="modal fade" id="addModelModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-info text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>Add AI Model
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addModelForm">
                <div class="modal-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="model_provider" class="form-label fw-bold">
                                <i class="fas fa-robot text-info me-1"></i>AI Provider *
                            </label>
                            <select class="form-select form-select-lg border-2" id="model_provider" name="provider_id" required>
                                <option value="">Select Provider...</option>
                                <?php foreach ($providers as $provider): ?>
                                <option value="<?php echo $provider['id']; ?>">
                                    <?php echo htmlspecialchars($provider['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Choose the AI provider for this model</div>
                        </div>
                        <div class="col-md-6">
                            <label for="model_name" class="form-label fw-bold">
                                <i class="fas fa-tag text-info me-1"></i>Model Name *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="model_name" name="name" required placeholder="GPT-4 Turbo">
                            <div class="form-text">Display name for the model</div>
                        </div>
                        <div class="col-md-6">
                            <label for="model_identifier" class="form-label fw-bold">
                                <i class="fas fa-code text-info me-1"></i>Model Identifier *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="model_identifier" name="model_id" required placeholder="gpt-4-turbo">
                            <div class="form-text">API model identifier (e.g., gpt-4, claude-3-sonnet)</div>
                        </div>
                        <div class="col-md-6">
                            <label for="model_type" class="form-label fw-bold">
                                <i class="fas fa-cogs text-info me-1"></i>Model Type *
                            </label>
                            <select class="form-select form-select-lg border-2" id="model_type" name="type" required>
                                <option value="">Select Type...</option>
                                <option value="text">Text Generation</option>
                                <option value="image">Image Generation</option>
                                <option value="multimodal">Multimodal</option>
                                <option value="embedding">Embedding</option>
                            </select>
                            <div class="form-text">Primary capability of this model</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">
                                <i class="fas fa-magic text-info me-1"></i>Model Capabilities
                            </label>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cap_translation" name="capabilities[]" value="translation">
                                        <label class="form-check-label" for="cap_translation">Translation</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cap_rewriting" name="capabilities[]" value="rewriting">
                                        <label class="form-check-label" for="cap_rewriting">Rewriting</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cap_summarization" name="capabilities[]" value="summarization">
                                        <label class="form-check-label" for="cap_summarization">Summarization</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cap_image_generation" name="capabilities[]" value="image_generation">
                                        <label class="form-check-label" for="cap_image_generation">Image Generation</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">Select all capabilities this model supports</div>
                        </div>
                        <div class="col-12">
                            <label for="model_system_prompt" class="form-label fw-bold">
                                <i class="fas fa-comment-dots text-info me-1"></i>System Prompt
                            </label>
                            <textarea class="form-control border-2" id="model_system_prompt" name="system_prompt" rows="3" placeholder="You are a helpful AI assistant..."></textarea>
                            <div class="form-text">Default system prompt for this model (optional)</div>
                        </div>
                        <div class="col-md-4">
                            <label for="model_max_tokens" class="form-label fw-bold">
                                <i class="fas fa-calculator text-info me-1"></i>Max Tokens
                            </label>
                            <input type="number" class="form-control form-control-lg border-2" id="model_max_tokens" name="max_tokens" min="1" placeholder="4096">
                            <div class="form-text">Maximum tokens per request</div>
                        </div>
                        <div class="col-md-4">
                            <label for="model_temperature" class="form-label fw-bold">
                                <i class="fas fa-thermometer-half text-info me-1"></i>Temperature
                            </label>
                            <input type="number" class="form-control form-control-lg border-2" id="model_temperature" name="temperature" min="0" max="2" step="0.1" value="0.7" placeholder="0.7">
                            <div class="form-text">Creativity level (0.0 - 2.0)</div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" id="model_is_active" name="is_active" value="1" checked>
                                <label class="form-check-label fw-bold" for="model_is_active">
                                    <i class="fas fa-toggle-on text-success me-1"></i>Active Model
                                </label>
                                <div class="form-text">Enable this model for use</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light border-0">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-info btn-lg">
                        <i class="fas fa-plus me-1"></i>Add Model
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Provider Modal -->
<div class="modal fade" id="editProviderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit AI Provider
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editProviderForm">
                <div class="modal-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_provider_name" class="form-label fw-bold">
                                <i class="fas fa-tag text-primary me-1"></i>Provider Name *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="edit_provider_name" name="name" required>
                            <div class="form-text">Display name for the AI provider</div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_provider_slug" class="form-label fw-bold">
                                <i class="fas fa-link text-primary me-1"></i>Slug *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="edit_provider_slug" name="slug" required>
                            <div class="form-text">URL-friendly identifier (auto-generated)</div>
                        </div>
                        <div class="col-12">
                            <label for="edit_provider_description" class="form-label fw-bold">
                                <i class="fas fa-align-left text-primary me-1"></i>Description
                            </label>
                            <textarea class="form-control border-2" id="edit_provider_description" name="description" rows="3"></textarea>
                            <div class="form-text">Brief description of the AI provider</div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_provider_api_url" class="form-label fw-bold">
                                <i class="fas fa-globe text-primary me-1"></i>API Base URL
                            </label>
                            <input type="url" class="form-control form-control-lg border-2" id="edit_provider_api_url" name="api_base_url">
                            <div class="form-text">Base URL for API requests</div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_provider_auth_type" class="form-label fw-bold">
                                <i class="fas fa-shield-alt text-primary me-1"></i>Authentication Type
                            </label>
                            <select class="form-select form-select-lg border-2" id="edit_provider_auth_type" name="auth_type">
                                <option value="bearer">Bearer Token</option>
                                <option value="api_key">API Key</option>
                                <option value="oauth">OAuth</option>
                                <option value="basic">Basic Auth</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="edit_provider_is_active" name="is_active" value="1">
                                <label class="form-check-label fw-bold" for="edit_provider_is_active">
                                    <i class="fas fa-toggle-on text-success me-1"></i>Active Provider
                                </label>
                                <div class="form-text">Enable this provider for use</div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="edit_provider_id" name="provider_id">
                </div>
                <div class="modal-footer bg-light border-0">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-1"></i>Update Provider
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit API Key Modal -->
<div class="modal fade" id="editApiKeyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-success text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit API Key
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editApiKeyForm">
                <div class="modal-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_api_key_provider" class="form-label fw-bold">
                                <i class="fas fa-robot text-success me-1"></i>AI Provider *
                            </label>
                            <select class="form-select form-select-lg border-2" id="edit_api_key_provider" name="provider_id" required>
                                <option value="">Select Provider...</option>
                                <?php foreach ($providers as $provider): ?>
                                <option value="<?php echo $provider['id']; ?>"><?php echo htmlspecialchars($provider['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_api_key_name" class="form-label fw-bold">
                                <i class="fas fa-tag text-success me-1"></i>Key Name *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="edit_api_key_name" name="name" required>
                            <div class="form-text">Friendly name for this API key</div>
                        </div>
                        <div class="col-12">
                            <label for="edit_api_key_value" class="form-label fw-bold">
                                <i class="fas fa-key text-success me-1"></i>API Key *
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg border-2" id="edit_api_key_value" name="api_key" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('edit_api_key_value', this)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Your API key (will be encrypted when stored)</div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="edit_api_key_is_default" name="is_default" value="1">
                                <label class="form-check-label fw-bold" for="edit_api_key_is_default">
                                    <i class="fas fa-star text-warning me-1"></i>Default Key
                                </label>
                                <div class="form-text">Use as default for this provider</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="edit_api_key_is_active" name="is_active" value="1">
                                <label class="form-check-label fw-bold" for="edit_api_key_is_active">
                                    <i class="fas fa-toggle-on text-success me-1"></i>Active Key
                                </label>
                                <div class="form-text">Enable this API key</div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="edit_api_key_id" name="api_key_id">
                </div>
                <div class="modal-footer bg-light border-0">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-save me-1"></i>Update API Key
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Model Modal -->
<div class="modal fade" id="editModelModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-info text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit AI Model
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editModelForm">
                <div class="modal-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_model_provider" class="form-label fw-bold">
                                <i class="fas fa-robot text-info me-1"></i>AI Provider *
                            </label>
                            <select class="form-select form-select-lg border-2" id="edit_model_provider" name="provider_id" required>
                                <option value="">Select Provider...</option>
                                <?php foreach ($providers as $provider): ?>
                                <option value="<?php echo $provider['id']; ?>"><?php echo htmlspecialchars($provider['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_model_name" class="form-label fw-bold">
                                <i class="fas fa-tag text-info me-1"></i>Model Name *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="edit_model_name" name="name" required>
                            <div class="form-text">Display name for the model</div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_model_identifier" class="form-label fw-bold">
                                <i class="fas fa-code text-info me-1"></i>Model Identifier *
                            </label>
                            <input type="text" class="form-control form-control-lg border-2" id="edit_model_identifier" name="model_id" required>
                            <div class="form-text">API model identifier (e.g., gpt-4, claude-3-sonnet)</div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_model_type" class="form-label fw-bold">
                                <i class="fas fa-cogs text-info me-1"></i>Model Type *
                            </label>
                            <select class="form-select form-select-lg border-2" id="edit_model_type" name="type" required>
                                <option value="">Select Type...</option>
                                <option value="text">Text Generation</option>
                                <option value="image">Image Generation</option>
                                <option value="multimodal">Multimodal</option>
                                <option value="embedding">Embedding</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">
                                <i class="fas fa-magic text-info me-1"></i>Model Capabilities
                            </label>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_cap_translation" name="capabilities[]" value="translation">
                                        <label class="form-check-label" for="edit_cap_translation">Translation</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_cap_rewriting" name="capabilities[]" value="rewriting">
                                        <label class="form-check-label" for="edit_cap_rewriting">Rewriting</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_cap_summarization" name="capabilities[]" value="summarization">
                                        <label class="form-check-label" for="edit_cap_summarization">Summarization</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="edit_cap_image_generation" name="capabilities[]" value="image_generation">
                                        <label class="form-check-label" for="edit_cap_image_generation">Image Generation</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="edit_model_max_tokens" class="form-label fw-bold">
                                <i class="fas fa-calculator text-info me-1"></i>Max Tokens
                            </label>
                            <input type="number" class="form-control form-control-lg border-2" id="edit_model_max_tokens" name="max_tokens" min="1">
                            <div class="form-text">Maximum tokens per request</div>
                        </div>
                        <div class="col-md-4">
                            <label for="edit_model_temperature" class="form-label fw-bold">
                                <i class="fas fa-thermometer-half text-info me-1"></i>Temperature
                            </label>
                            <input type="number" class="form-control form-control-lg border-2" id="edit_model_temperature" name="temperature" min="0" max="2" step="0.1">
                            <div class="form-text">Creativity level (0.0 - 2.0)</div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" id="edit_model_is_active" name="is_active" value="1">
                                <label class="form-check-label fw-bold" for="edit_model_is_active">
                                    <i class="fas fa-toggle-on text-success me-1"></i>Active Model
                                </label>
                                <div class="form-text">Enable this model for use</div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="edit_model_id" name="model_id_hidden">
                </div>
                <div class="modal-footer bg-light border-0">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-info btn-lg">
                        <i class="fas fa-save me-1"></i>Update Model
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-danger text-white border-0">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <p id="deleteConfirmMessage" class="fs-6 mb-3">Are you sure you want to delete this item?</p>
                <div class="alert alert-warning border-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Smart Delete:</strong> All related data will also be removed automatically.
                </div>
            </div>
            <div class="modal-footer bg-light border-0">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger btn-lg" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let deleteAction = null;
let deleteId = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from provider name
    document.getElementById('provider_name')?.addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
        document.getElementById('provider_slug').value = slug;
    });

    // Form submissions
    setupFormSubmissions();
});

// Setup form submissions
function setupFormSubmissions() {
    // Add Provider Form
    document.getElementById('addProviderForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'add_provider', 'addProviderModal');
    });

    // Add API Key Form
    document.getElementById('addApiKeyForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'add_api_key', 'addApiKeyModal');
    });

    // Add Model Form
    document.getElementById('addModelForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'add_model', 'addModelModal');
    });

    // Edit Provider Form
    document.getElementById('editProviderForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'edit_provider', 'editProviderModal');
    });

    // Edit API Key Form
    document.getElementById('editApiKeyForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'edit_api_key', 'editApiKeyModal');
    });

    // Edit Model Form
    document.getElementById('editModelForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'edit_model', 'editModelModal');
    });

    // Auto-generate slug for edit provider form
    document.getElementById('edit_provider_name')?.addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
        document.getElementById('edit_provider_slug').value = slug;
    });
}

// Submit form via AJAX
function submitForm(form, action, modalId) {
    const formData = new FormData(form);
    formData.append('ajax', '1');
    formData.append('action', action);

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    submitBtn.disabled = true;

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showToast('success', data.message);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            modal.hide();

            // Reset form
            form.reset();

            // Refresh the page to show new data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'An error occurred while saving.');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Show modal functions
function showAddProviderModal() {
    const modal = new bootstrap.Modal(document.getElementById('addProviderModal'));
    modal.show();
}

function showAddApiKeyModal() {
    const modal = new bootstrap.Modal(document.getElementById('addApiKeyModal'));
    modal.show();
}

function showAddModelModal() {
    const modal = new bootstrap.Modal(document.getElementById('addModelModal'));
    modal.show();
}

function showAddWorkflowModal() {
    // TODO: Implement workflow modal
    showToast('info', 'Workflow management coming soon!');
}

// Delete functions
function deleteProvider(id, name) {
    showDeleteConfirmation('provider', id, name, `Are you sure you want to delete the provider "${name}"?`);
}

function deleteApiKey(id, name) {
    showDeleteConfirmation('api_key', id, name, `Are you sure you want to delete the API key "${name}"?`);
}

function deleteModel(id, name) {
    showDeleteConfirmation('model', id, name, `Are you sure you want to delete the model "${name}"?`);
}

function deleteWorkflow(id, name) {
    showDeleteConfirmation('workflow', id, name, `Are you sure you want to delete the workflow "${name}"?`);
}

// Show delete confirmation modal
function showDeleteConfirmation(type, id, name, message) {
    deleteAction = type;
    deleteId = id;

    document.getElementById('deleteConfirmMessage').textContent = message;

    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// Confirm delete action
document.getElementById('confirmDeleteBtn')?.addEventListener('click', function() {
    if (!deleteAction || !deleteId) return;

    const actionMap = {
        'provider': 'delete_provider',
        'api_key': 'delete_api_key',
        'model': 'delete_model',
        'workflow': 'delete_workflow'
    };

    const formData = new FormData();
    formData.append('ajax', '1');
    formData.append('action', actionMap[deleteAction]);
    formData.append(deleteAction + '_id', deleteId);

    // Show loading state
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
    this.disabled = true;

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();

            // Remove the item from the page
            const itemElement = document.querySelector(`[data-${deleteAction.replace('_', '-')}-id="${deleteId}"]`);
            if (itemElement) {
                itemElement.style.transition = 'all 0.3s ease';
                itemElement.style.opacity = '0';
                itemElement.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    itemElement.remove();
                }, 300);
            }
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'An error occurred while deleting.');
    })
    .finally(() => {
        // Restore button state
        this.innerHTML = '<i class="fas fa-trash me-1"></i>Delete';
        this.disabled = false;
        deleteAction = null;
        deleteId = null;
    });
});

// Edit functions
function editProvider(id) {
    // Get provider data from the page
    const providerCard = document.querySelector(`[data-provider-id="${id}"]`);
    if (!providerCard) {
        showToast('error', 'Provider not found');
        return;
    }

    // Extract data from the card (you might want to fetch from server instead)
    fetch(`<?php echo BASE_URL; ?>/?page=ai_settings&ajax=1&action=get_provider&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const provider = data.provider;

                // Populate edit form
                document.getElementById('edit_provider_id').value = provider.id;
                document.getElementById('edit_provider_name').value = provider.name || '';
                document.getElementById('edit_provider_slug').value = provider.slug || '';
                document.getElementById('edit_provider_description').value = provider.description || '';
                document.getElementById('edit_provider_api_url').value = provider.api_base_url || '';
                document.getElementById('edit_provider_auth_type').value = provider.auth_type || 'bearer';
                document.getElementById('edit_provider_is_active').checked = provider.is_active == 1;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editProviderModal'));
                modal.show();
            } else {
                showToast('error', data.message || 'Failed to load provider data');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to load provider data');
        });
}

function editApiKey(id) {
    // Fetch API key data
    fetch(`<?php echo BASE_URL; ?>/?page=ai_settings&ajax=1&action=get_api_key&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const apiKey = data.api_key;

                // Populate edit form
                document.getElementById('edit_api_key_id').value = apiKey.id;
                document.getElementById('edit_api_key_provider').value = apiKey.provider_id || '';
                document.getElementById('edit_api_key_name').value = apiKey.name || '';
                document.getElementById('edit_api_key_value').value = apiKey.api_key || '';
                document.getElementById('edit_api_key_is_default').checked = apiKey.is_default == 1;
                document.getElementById('edit_api_key_is_active').checked = apiKey.is_active == 1;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editApiKeyModal'));
                modal.show();
            } else {
                showToast('error', data.message || 'Failed to load API key data');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to load API key data');
        });
}

function editModel(id) {
    // Fetch model data
    fetch(`<?php echo BASE_URL; ?>/?page=ai_settings&ajax=1&action=get_model&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const model = data.model;

                // Populate edit form
                document.getElementById('edit_model_id').value = model.id;
                document.getElementById('edit_model_provider').value = model.provider_id || '';
                document.getElementById('edit_model_name').value = model.name || '';
                document.getElementById('edit_model_identifier').value = model.model_id || '';
                document.getElementById('edit_model_type').value = model.type || '';
                document.getElementById('edit_model_max_tokens').value = model.max_tokens || '';
                document.getElementById('edit_model_temperature').value = model.temperature || '';
                document.getElementById('edit_model_is_active').checked = model.is_active == 1;

                // Handle capabilities checkboxes
                const capabilities = model.capabilities ? model.capabilities.split(',') : [];
                document.getElementById('edit_cap_translation').checked = capabilities.includes('translation');
                document.getElementById('edit_cap_rewriting').checked = capabilities.includes('rewriting');
                document.getElementById('edit_cap_summarization').checked = capabilities.includes('summarization');
                document.getElementById('edit_cap_image_generation').checked = capabilities.includes('image_generation');

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editModelModal'));
                modal.show();
            } else {
                showToast('error', data.message || 'Failed to load model data');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to load model data');
        });
}

function editWorkflow(id) {
    showToast('info', 'Workflow editing coming soon!');
}

// Password visibility toggle
function togglePasswordVisibility(inputId, button) {
    const input = document.getElementById(inputId);
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// Refresh data
function refreshData() {
    window.location.reload();
}

// Toast notification system
function showToast(type, message) {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast');
    existingToasts.forEach(toast => toast.remove());

    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastElement = document.createElement('div');
    toastElement.className = 'toast';
    toastElement.setAttribute('role', 'alert');

    const iconMap = {
        'success': 'fas fa-check-circle text-success',
        'error': 'fas fa-exclamation-circle text-danger',
        'warning': 'fas fa-exclamation-triangle text-warning',
        'info': 'fas fa-info-circle text-info'
    };

    toastElement.innerHTML = `
        <div class="toast-header">
            <i class="${iconMap[type]} me-2"></i>
            <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    toastContainer.appendChild(toastElement);

    // Show toast
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 5000 : 3000
    });
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Tab persistence
document.addEventListener('DOMContentLoaded', function() {
    // Get active tab from URL or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('section') || localStorage.getItem('aiSettingsActiveTab') || 'providers';

    // Activate the tab
    const tabButton = document.getElementById(activeTab + '-tab');
    if (tabButton) {
        tabButton.click();
    }

    // Save active tab when changed
    document.querySelectorAll('#aiSettingsTabs button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const tabId = e.target.id.replace('-tab', '');
            localStorage.setItem('aiSettingsActiveTab', tabId);

            // Update URL without page reload
            const url = new URL(window.location);
            url.searchParams.set('section', tabId);
            window.history.replaceState({}, '', url);
        });
    });
});
</script>