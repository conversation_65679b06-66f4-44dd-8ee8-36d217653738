<?php
/**
 * Fix Models Issues
 * 
 * This script fixes common issues with AI models not appearing.
 */

// Load configuration
require_once 'config.php';
require_once 'includes/Database.php';

// Initialize database connection
$db = new Database();

// Include tool header
$pageTitle = 'Fix Models Issues';
$currentPage = 'fix_models';
include_once 'includes/tool_header.php';

echo '<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Fix AI Models Issues</h5>
    </div>
    <div class="card-body">
        <pre>';

echo "Fixing AI Models Issues...\n\n";

try {
    $fixesApplied = 0;
    
    // 1. Check and fix orphaned models
    echo "=== FIXING ORPHANED MODELS ===\n";
    $orphanedModels = $db->query("
        SELECT m.* 
        FROM ai_models m 
        LEFT JOIN ai_providers p ON m.provider_id = p.id 
        WHERE p.id IS NULL
    ");
    
    if (!empty($orphanedModels)) {
        echo "Found " . count($orphanedModels) . " orphaned models.\n";
        
        // Get the first active provider to reassign orphaned models
        $firstProvider = $db->getRow("SELECT id, name FROM ai_providers WHERE is_active = 1 ORDER BY id LIMIT 1");
        
        if ($firstProvider) {
            echo "Reassigning orphaned models to provider: {$firstProvider['name']}\n";
            
            foreach ($orphanedModels as $model) {
                $db->update('ai_models', 
                    ['provider_id' => $firstProvider['id']], 
                    'id = ?', 
                    [$model['id']]
                );
                echo "  • Fixed model: {$model['name']} (ID: {$model['id']})\n";
                $fixesApplied++;
            }
        } else {
            echo "❌ No active providers found to reassign orphaned models.\n";
            echo "Please run setup_default_providers.php first.\n";
        }
    } else {
        echo "✅ No orphaned models found.\n";
    }
    
    // 2. Activate inactive providers that have models
    echo "\n=== ACTIVATING PROVIDERS WITH MODELS ===\n";
    $inactiveProvidersWithModels = $db->query("
        SELECT DISTINCT p.id, p.name, COUNT(m.id) as model_count
        FROM ai_providers p
        JOIN ai_models m ON p.id = m.provider_id
        WHERE p.is_active = 0
        GROUP BY p.id, p.name
    ");
    
    if (!empty($inactiveProvidersWithModels)) {
        echo "Found " . count($inactiveProvidersWithModels) . " inactive providers with models.\n";
        
        foreach ($inactiveProvidersWithModels as $provider) {
            $db->update('ai_providers', 
                ['is_active' => 1], 
                'id = ?', 
                [$provider['id']]
            );
            echo "  • Activated provider: {$provider['name']} ({$provider['model_count']} models)\n";
            $fixesApplied++;
        }
    } else {
        echo "✅ No inactive providers with models found.\n";
    }
    
    // 3. Check if default providers exist
    echo "\n=== CHECKING DEFAULT PROVIDERS ===\n";
    $providersCount = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    
    if ($providersCount == 0) {
        echo "❌ No active providers found.\n";
        echo "Creating default NOVITA provider...\n";
        
        // Create default NOVITA provider
        $providerId = $db->insert('ai_providers', [
            'name' => 'NOVITA AI',
            'slug' => 'novita',
            'description' => 'AI platform providing text and image generation capabilities.',
            'website' => 'https://novita.ai',
            'api_base_url' => 'https://api.novita.ai/v1',
            'auth_type' => 'bearer',
            'is_active' => 1
        ]);
        
        echo "  • Created NOVITA AI provider (ID: $providerId)\n";
        $fixesApplied++;
        
        // Reassign any models without valid providers to NOVITA
        $modelsWithoutProvider = $db->query("
            SELECT m.* 
            FROM ai_models m 
            LEFT JOIN ai_providers p ON m.provider_id = p.id 
            WHERE p.id IS NULL OR p.is_active = 0
        ");
        
        if (!empty($modelsWithoutProvider)) {
            foreach ($modelsWithoutProvider as $model) {
                $db->update('ai_models', 
                    ['provider_id' => $providerId], 
                    'id = ?', 
                    [$model['id']]
                );
                echo "  • Reassigned model '{$model['name']}' to NOVITA AI\n";
                $fixesApplied++;
            }
        }
    } else {
        echo "✅ Found $providersCount active providers.\n";
    }
    
    // 4. Verify models are now visible
    echo "\n=== VERIFICATION ===\n";
    $visibleModels = $db->query("
        SELECT m.name, p.name as provider_name
        FROM ai_models m
        JOIN ai_providers p ON m.provider_id = p.id
        WHERE p.is_active = 1
        ORDER BY p.name, m.name
    ");
    
    if (!empty($visibleModels)) {
        echo "✅ Found " . count($visibleModels) . " visible models:\n";
        foreach ($visibleModels as $model) {
            echo "  • {$model['provider_name']} / {$model['name']}\n";
        }
    } else {
        echo "❌ No visible models found after fixes.\n";
    }
    
    // 5. Summary
    echo "\n=== SUMMARY ===\n";
    if ($fixesApplied > 0) {
        echo "✅ Applied $fixesApplied fixes successfully!\n";
        echo "Please refresh the AI Settings > Models page to see the changes.\n";
    } else {
        echo "ℹ️  No fixes were needed.\n";
    }
    
    // 6. Additional recommendations
    echo "\n=== RECOMMENDATIONS ===\n";
    
    $totalModels = $db->getValue("SELECT COUNT(*) FROM ai_models");
    $totalProviders = $db->getValue("SELECT COUNT(*) FROM ai_providers WHERE is_active = 1");
    
    if ($totalProviders < 2) {
        echo "• Consider running setup_default_providers.php to add more AI providers\n";
    }
    
    if ($totalModels < 3) {
        echo "• Add more models through AI Settings > Models for better functionality\n";
    }
    
    echo "• Test your models in the AI Lab after adding API keys\n";
    echo "• Configure workflows in AI Settings > Workflows\n";
    
} catch (Exception $e) {
    echo "❌ Error during fix: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo '</pre>
    </div>
    <div class="card-footer bg-white">
        <div class="row">
            <div class="col-md-3">
                <a href="' . BASE_URL . '/debug_models.php" class="btn btn-info">
                    <i class="fas fa-bug me-1"></i> Debug Models
                </a>
            </div>
            <div class="col-md-3">
                <a href="' . BASE_URL . '/setup_default_providers.php" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i> Setup Providers
                </a>
            </div>
            <div class="col-md-3">
                <a href="' . BASE_URL . '/?page=ai_settings&section=models" class="btn btn-primary">
                    <i class="fas fa-brain me-1"></i> View Models
                </a>
            </div>
            <div class="col-md-3">
                <a href="' . BASE_URL . '/?page=ai_settings&section=providers" class="btn btn-outline-secondary">
                    <i class="fas fa-robot me-1"></i> View Providers
                </a>
            </div>
        </div>
    </div>
</div>';

// Include tool footer
include_once 'includes/tool_footer.php';
?>
